# ESP32-S3-TOUCH-LCD-7 Display Configuration
# Optimized settings for video display performance

# Partition Table Configuration
CONFIG_PARTITION_TABLE_CUSTOM=y
CONFIG_PARTITION_TABLE_CUSTOM_FILENAME="partitions.csv"

# CPU and Memory Configuration
CONFIG_FREERTOS_HZ=1000
CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ_240=y

# Flash Configuration
CONFIG_ESPTOOLPY_FLASHMODE_QIO=y
CONFIG_ESPTOOLPY_FLASHFREQ_120M=y

# PSRAM Configuration
CONFIG_SPIRAM=y
CONFIG_SPIRAM_MODE_OCT=y
CONFIG_IDF_EXPERIMENTAL_FEATURES=y
CONFIG_SPIRAM_SPEED_120M=y
CONFIG_SPIRAM_FETCH_INSTRUCTIONS=y
CONFIG_SPIRAM_RODATA=y
CONFIG_ESP32S3_DATA_CACHE_LINE_64B=y

# Compiler Optimization
CONFIG_COMPILER_OPTIMIZATION_PERF=y

# LVGL Configuration (will be enabled when esp_lvgl_port is available)
# CONFIG_LV_MEM_CUSTOM=y
# CONFIG_LV_MEMCPY_MEMSET_STD=y
# CONFIG_LV_ATTRIBUTE_FAST_MEM=y

# Display Configuration
CONFIG_MODE_LOCAL_DISPLAY=y
CONFIG_ENABLE_TOUCH=y
CONFIG_DISPLAY_BUFFER_SIZE=2

# Frame Configuration
CONFIG_MANUAL_SETTING=y
CONFIG_SIZE_320x240=y
CONFIG_FORMAT_MJPG=y
CONFIG_FRAME_RATE=30

# USB Host Configuration
CONFIG_USB_HOST_CONTROL_TRANSFER_MAX_SIZE=1024
CONFIG_USB_HOST_HW_BUFFER_BIAS_BALANCED=y

# Log Level Configuration
CONFIG_LOG_DEFAULT_LEVEL_INFO=y
CONFIG_LOG_MAXIMUM_LEVEL_VERBOSE=y

# Task Configuration
CONFIG_FREERTOS_UNICORE=n
CONFIG_ESP_TASK_WDT_TIMEOUT_S=10

# Memory Configuration
CONFIG_ESP_MAIN_TASK_STACK_SIZE=8192
CONFIG_ESP_SYSTEM_EVENT_TASK_STACK_SIZE=4096

# LCD Panel Configuration
CONFIG_LCD_RGB_ISR_IRAM_SAFE=y
CONFIG_LCD_RGB_RESTART_IN_VSYNC=y

# I2C Configuration
CONFIG_I2C_ENABLE_DEBUG_LOG=n

# Bootloader Configuration
CONFIG_BOOTLOADER_LOG_LEVEL_WARN=y

# Application Size Configuration
CONFIG_APP_COMPILE_TIME_DATE=n
CONFIG_APP_EXCLUDE_PROJECT_VER_VAR=y
CONFIG_APP_EXCLUDE_PROJECT_NAME_VAR=y

# Component Configuration
CONFIG_LWIP_MAX_SOCKETS=16
CONFIG_LWIP_SO_REUSE=y
CONFIG_LWIP_SO_REUSE_RXTOALL=y

# HTTP Server Configuration
CONFIG_HTTPD_MAX_REQ_HDR_LEN=1024
CONFIG_HTTPD_MAX_URI_LEN=512
