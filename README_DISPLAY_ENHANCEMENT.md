# ESP32-S3-TOUCH-LCD-7 Display Enhancement

This enhancement adds local display functionality to the existing ESP-IDF USB UVC camera streaming project, enabling video display on the ESP32-S3-TOUCH-LCD-7 panel as an alternative to WiFi streaming.

## Features Added

### 🖥️ Local Display Support
- **7-inch LCD Display**: 800x480 resolution RGB display support
- **Touch Screen**: Optional capacitive touch screen integration
- **Real-time Video**: Live camera stream display with optimized performance
- **Dual Buffer System**: Smooth video playback with double buffering

### ⚙️ Configuration System
- **Mode Selection**: Choose between WiFi streaming and local display
- **Runtime Configuration**: Dynamic mode switching support
- **Performance Tuning**: Optimized settings for video display

### 🚀 Performance Optimizations
- **DMA Optimization**: Efficient memory transfers
- **PSRAM Usage**: Large frame buffers in external RAM
- **Cache Optimization**: Optimized for ESP32-S3 architecture
- **Frame Rate**: Up to 30 FPS video display

## Hardware Requirements

### ESP32-S3-TOUCH-LCD-7 Panel
- **Display**: 7-inch IPS LCD, 800x480 resolution
- **Touch**: GT911 capacitive touch controller (optional)
- **Interface**: RGB parallel interface
- **I/O Expander**: CH422G for additional control signals

### Pin Configuration
```
RGB Data Pins (16-bit RGB565):
- Blue:  GPIO14, GPIO38, GPIO18, GPIO17, GPIO10
- Green: GPIO39, GPIO0, GPIO45, GPIO48, GPIO47, GPIO21
- Red:   GPIO1, GPIO2, GPIO42, GPIO41, GPIO40

Control Pins:
- PCLK:  GPIO7  (Pixel Clock)
- HSYNC: GPIO46 (Horizontal Sync)
- VSYNC: GPIO3  (Vertical Sync)
- DE:    GPIO5  (Data Enable)

Touch Interface (I2C):
- SDA:   GPIO8
- SCL:   GPIO9
- IRQ:   GPIO4
```

## Software Architecture

### New Components Added

1. **Display Driver** (`display_driver.c/h`)
   - RGB LCD panel initialization
   - Frame buffer management
   - JPEG to RGB565 conversion
   - Touch screen support

2. **Configuration System** (`app_config.c/h`)
   - Mode selection (WiFi/Display/Dual)
   - Runtime configuration management
   - Performance settings

3. **Enhanced Main Application**
   - Integrated display support
   - Conditional compilation for different modes
   - Optimized frame processing pipeline

### Frame Processing Pipeline

```
USB UVC Camera → Frame Capture → Format Conversion → Output Selection
                                      ↓
                              ┌─────────────────┐
                              │   WiFi Mode     │ → HTTP Streaming
                              │ Display Mode    │ → LCD Display
                              │   Dual Mode     │ → Both Outputs
                              └─────────────────┘
```

## Configuration Options

### Kconfig Settings

```bash
# Select output mode
CONFIG_MODE_WIFI_STREAMING=y    # WiFi streaming only
CONFIG_MODE_LOCAL_DISPLAY=y     # Local display only

# Display settings
CONFIG_ENABLE_TOUCH=y           # Enable touch screen
CONFIG_DISPLAY_BUFFER_SIZE=2    # Number of frame buffers (1-3)

# Frame settings
CONFIG_SIZE_320x240=y           # Frame resolution
CONFIG_FORMAT_MJPG=y            # Frame format
CONFIG_FRAME_RATE=30            # Frame rate
```

### Performance Configuration

Use the provided `sdkconfig.defaults.display` for optimal performance:

```bash
cp sdkconfig.defaults.display sdkconfig.defaults
idf.py menuconfig  # Fine-tune if needed
```

## Build and Flash Instructions

### 1. Setup Environment
```bash
# Ensure ESP-IDF v5.5.0 is installed
. $IDF_PATH/export.sh

# Navigate to project directory
cd esp-idf-video-streaming-main
```

### 2. Configure Project
```bash
# Set target
idf.py set-target esp32s3

# Configure for display mode
cp sdkconfig.defaults.display sdkconfig.defaults

# Optional: customize configuration
idf.py menuconfig
```

### 3. Build and Flash
```bash
# Build project
idf.py build

# Flash to device
idf.py -p /dev/ttyUSB0 flash monitor
```

## Usage

### WiFi Streaming Mode (Default)
- Connects to WiFi network
- Streams video via HTTP server
- Access via web browser: `http://[ESP32_IP]:8080`

### Local Display Mode
- Displays video directly on LCD panel
- Touch screen support (if enabled)
- No WiFi connection required

### Dual Mode (Future Enhancement)
- Simultaneous WiFi streaming and local display
- Requires additional memory optimization

## Performance Tuning

### Memory Configuration
```c
// Optimal PSRAM settings
CONFIG_SPIRAM=y
CONFIG_SPIRAM_MODE_OCT=y
CONFIG_SPIRAM_SPEED_120M=y
CONFIG_SPIRAM_FETCH_INSTRUCTIONS=y
CONFIG_SPIRAM_RODATA=y
```

### CPU Configuration
```c
// Maximum performance
CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ_240=y
CONFIG_FREERTOS_HZ=1000
CONFIG_COMPILER_OPTIMIZATION_PERF=y
```

### Display Optimization
```c
// LCD performance
CONFIG_LCD_RGB_ISR_IRAM_SAFE=y
CONFIG_LCD_RGB_RESTART_IN_VSYNC=y
```

## Troubleshooting

### Common Issues

1. **Display Not Working**
   - Check pin connections
   - Verify I/O expander initialization
   - Check power supply (5V required)

2. **Poor Frame Rate**
   - Increase CPU frequency to 240MHz
   - Enable PSRAM optimizations
   - Reduce frame resolution if needed

3. **Touch Not Responding**
   - Verify I2C connections
   - Check touch controller initialization
   - Ensure CONFIG_ENABLE_TOUCH=y

### Debug Commands
```bash
# Monitor logs
idf.py monitor

# Check memory usage
idf.py size

# Analyze performance
idf.py app-flash monitor
```

## API Reference

### Display Driver Functions
```c
// Initialize display
esp_err_t display_driver_init(void);

// Show frame on display
esp_err_t display_show_frame(const uint8_t *frame_data, 
                            size_t frame_size, bool is_jpeg);

// Check if display is ready
bool display_is_initialized(void);
```

### Configuration Functions
```c
// Initialize configuration
esp_err_t app_config_init(void);

// Check current mode
bool app_config_is_display_enabled(void);
bool app_config_is_wifi_enabled(void);
```

## Future Enhancements

- [ ] LVGL GUI integration for advanced UI
- [ ] Video recording to SD card
- [ ] Multiple camera support
- [ ] Wireless display streaming
- [ ] Touch-based camera controls
- [ ] Image processing filters

## License

This enhancement maintains the same license as the original project (Apache 2.0).

## Contributing

1. Fork the repository
2. Create feature branch
3. Test thoroughly on hardware
4. Submit pull request with detailed description

## Support

For issues specific to the display enhancement:
1. Check hardware connections
2. Verify configuration settings
3. Review debug logs
4. Submit issue with full details
