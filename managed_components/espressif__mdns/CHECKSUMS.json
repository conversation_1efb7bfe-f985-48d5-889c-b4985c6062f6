{"version": "1.0", "algorithm": "sha256", "created_at": "2025-05-21T16:48:33.954015+00:00", "files": [{"path": "mdns_console.c", "size": 47930, "hash": "404e6b43423af614db393422c5af6e7898529f93c139b45470b049472bb1329d"}, {"path": "CMakeLists.txt", "size": 1302, "hash": "eaccef84e77e9b6eea8761cfbb18b9e24f62889144bd53bd6083c8f26e5ce6e9"}, {"path": "LICENSE", "size": 11358, "hash": "cfc7749b96f63bd31c3c42b5c471bf756814053e847c10f3eb003417bc523d30"}, {"path": "CHANGELOG.md", "size": 67010, "hash": "11619c66f4e205ce8c8b297f8ffe6f9b575ffe15e5594a26b670398b44cc5f92"}, {"path": "mdns_networking_socket.c", "size": 17555, "hash": "0422fd943e04dc600c9c8e568c3041eb8b5e86c839508c610128dfb5f9945a7d"}, {"path": "idf_component.yml", "size": 535, "hash": "1ad7d0f3eab912e608d4f5e0be3dbedb20eaf344be613e9092dcfcdd135bdedc"}, {"path": "mdns_mem_caps.c", "size": 2407, "hash": "eeaf91732c95432c97bd44cd1211e017f5d6e9bf9d52fe13b988099a9c9fc9ad"}, {"path": "Kconfig", "size": 6794, "hash": "67a2b3a13edd21a09f003babc216232df045b7e55ce6e4a90c7cf63ed2db0771"}, {"path": ".cz.yaml", "size": 216, "hash": "e4d7e2be4e90ca59ef0167744ee1b08831478a3d17e55b956783fa139a6ae257"}, {"path": "README.md", "size": 673, "hash": "05ae92097f344b060cbf90ae21c9fe9953d53188a363f45f3ce2af945a8b6244"}, {"path": "mdns.c", "size": 277909, "hash": "ee069fe92df66c51ad7362e05d021464c0d9b77aa716ac8f0e3c2207865cd970"}, {"path": "mem_prefix_script.py", "size": 1370, "hash": "5320bff3afb9fca57e5a12a9a217f7d68bccc28300d4a96b6aab8aa648206575"}, {"path": "mdns_networking_lwip.c", "size": 11180, "hash": "33bd6ed9ca58b4ce62ee6b7f1b419a64826e2a4242a2b0ac91107012190cadb7"}, {"path": "private_include/mdns_networking.h", "size": 1408, "hash": "12456bd2f36907252277db40f922cfd666fac4f59c02450a8652f9924db55cd5"}, {"path": "private_include/mdns_mem_caps.h", "size": 1483, "hash": "67f749086445e9eb9ba8d00120eca699e9b16865c1c8ccdf4449ad9cd6c9076d"}, {"path": "private_include/mdns_private.h", "size": 13826, "hash": "4bb7bcc7bcacfbad4ff6adc6f453105f5e7ad62f0d622f2b94148302a16ba4b6"}, {"path": "include/mdns.h", "size": 36017, "hash": "883cae69f6edc3b5743ec83fa698e416be3a69a719ad66245728837d334f1ae6"}, {"path": "include/mdns_console.h", "size": 308, "hash": "7b06003a193a2b602dc38e2f8d7b34c4ed3043e02c57345565f06030464d946a"}, {"path": "examples/query_advertise/CMakeLists.txt", "size": 239, "hash": "d0442b76ac4c9d8bd895e06b4097d1b5b878fcc5ba5833b04221a8c630bd623c"}, {"path": "examples/query_advertise/sdkconfig.ci.eth_no_ipv4", "size": 478, "hash": "e0c9f0ada36d3ede6772e5161293dcabb67aaf13bf3e88255397f435ec07f626"}, {"path": "examples/query_advertise/sdkconfig.ci.eth_def", "size": 489, "hash": "e6c46e51ac75407c09b32b6361383727337ddea0275555eb82f4f7908196ca6c"}, {"path": "examples/query_advertise/sdkconfig.ci.eth_socket", "size": 521, "hash": "f3bbfa2a648d3908d7b8235d3c3d62714bcadf5aa160ae9347d285e6512e7eb1"}, {"path": "examples/query_advertise/README.md", "size": 4517, "hash": "6e41319d31b1bc66c4a31d68516a92986ac21beeb56eabcf0c13a5e84466e878"}, {"path": "examples/query_advertise/sdkconfig.ci.eth_custom_netif", "size": 650, "hash": "be7ed6ad54d9e5fa36bbc5c24be90183e0f53f212596e956e5d8f23a1beaf14a"}, {"path": "examples/query_advertise/sdkconfig.ci.eth_no_ipv6", "size": 508, "hash": "6ec8aff6b96fbc6a3884cc7a24c9e292c7ddda4933ca708359c860d8262d8a4d"}, {"path": "examples/query_advertise/pytest_mdns.py", "size": 8744, "hash": "a62d84faa926c5baf09a5e826713419b45738d0134a7b5554c17c15cf0850f79"}, {"path": "examples/query_advertise/main/CMakeLists.txt", "size": 88, "hash": "d02d665083ea78f8aa519b770ea9c4c348889f7c4964b5d224e4898f08fc6adb"}, {"path": "examples/query_advertise/main/mdns_example_main.c", "size": 14341, "hash": "c12ed545332a730a4da204d27ec47cba762cf6b5c61e49b94056692f864d4528"}, {"path": "examples/query_advertise/main/idf_component.yml", "size": 228, "hash": "5115862e7e8d2ace9eb87e34edc0be447a6bce021b579eb79bd45fadfe2740c0"}, {"path": "examples/query_advertise/main/Kconfig.projbuild", "size": 1920, "hash": "544af51bd80b0a8be3930329b3611219e95c54a58ff2947ec22a458bba5e417f"}, {"path": "tests/unit_test/sdkconfig.ci", "size": 94, "hash": "8da7589f815a526c0e9bc8291cd1e58864041b80bec592a91840a5c8847cffa6"}, {"path": "tests/unit_test/CMakeLists.txt", "size": 254, "hash": "dc9e538d4f5d51f4a419bbb40eefb01b88fb6392f03f1d2a7217565670a25bc4"}, {"path": "tests/unit_test/pytest_app_mdns.py", "size": 213, "hash": "b15b764b8d0fcf025dbe7ae4d06f0077537749fba52bef09b46ac2ebc12c82a8"}, {"path": "tests/unit_test/sdkconfig.defaults", "size": 68, "hash": "bf225acae456eb714b977921a0e9d29bdce06d7113f5544593758357810adc56"}, {"path": "tests/test_apps/pytest_mdns_app.py", "size": 11562, "hash": "e05f0a3ed9b65a7bea960ae56e435b10d7ab1c1b950fda81147ac08ec972b17c"}, {"path": "tests/test_apps/sdkconfig.ci", "size": 426, "hash": "269731303d830c4b4833ef64d82b1415ae8cd39a2fd462d2535e95dab8521d72"}, {"path": "tests/test_apps/CMakeLists.txt", "size": 243, "hash": "664d50a77bf6a7fd8f4c0c465c23b461f1cb747f37146f2a8b6d449ccfa50b8f"}, {"path": "tests/test_apps/README.md", "size": 2600, "hash": "83e5d96b712d48a2a280126d6722c2d1780cb56483dfc42af088c6dc407830f6"}, {"path": "tests/test_apps/sdkconfig.defaults", "size": 80, "hash": "35419efd9750423871740ce1b16035eccb27abe99b058b5e1558c7901c8c0870"}, {"path": "tests/test_afl_fuzz_host/CMakeLists.txt", "size": 246, "hash": "7f765f63b68f17bc3a9a0876b500ea97d7ef3005ff2c44ae8b5672a7eae0c182"}, {"path": "tests/test_afl_fuzz_host/mdns_di.h", "size": 2623, "hash": "6b764afeb937f09f4712b44e50b60977ffa717174affa32fd1e1095717f54e27"}, {"path": "tests/test_afl_fuzz_host/Makefile", "size": 2986, "hash": "0542081a2b12b8ec7227300fb3891c84574262a6c05db204aab64d0c9e500976"}, {"path": "tests/test_afl_fuzz_host/esp_netif_mock.c", "size": 1102, "hash": "29605f11fe6cb3ab2e47a94e371b331279793c3b4e44971137d354cd69199886"}, {"path": "tests/test_afl_fuzz_host/sdkconfig.h", "size": 19870, "hash": "f4a9a68df6c7555ca58aa46201bd3450acbda1d3416d6aefe2f815195d2f6ad6"}, {"path": "tests/test_afl_fuzz_host/esp32_mock.h", "size": 3872, "hash": "dc9388f5499ff9dff2fe6cb949ad5d032ca9ac1d5d25352d70d1f21f28fe4c4a"}, {"path": "tests/test_afl_fuzz_host/README.md", "size": 3162, "hash": "e4ea309397d3ba457f8da7c7113aff012f0f53e9a058595f81dd17cf8d615b21"}, {"path": "tests/test_afl_fuzz_host/mdns_mock.h", "size": 641, "hash": "59d009c44873b19b9c6dfe8dd3a9d05bc4b1512086b9a30dbb4c3d27309b38eb"}, {"path": "tests/test_afl_fuzz_host/input_packets.txt", "size": 9036, "hash": "0e766ea9238c83fa5a6c131117d297ecd2fc2e362a7ca9756ee044198c99e0c3"}, {"path": "tests/test_afl_fuzz_host/test.c", "size": 8175, "hash": "65730ef39b8b193aab83d391f694a6aa4565aca6ed5b09e7e97385b6ca7ab65a"}, {"path": "tests/test_afl_fuzz_host/esp_attr.h", "size": 327, "hash": "416714c27021d76740af13d7f00274a22b93398c3f992c03bd9ad50caa2dbe95"}, {"path": "tests/test_afl_fuzz_host/esp32_mock.c", "size": 3030, "hash": "cc9aa786fdd4c67a9039d72eaf31498309276b399a06106d068a9bcb0b49c1ee"}, {"path": "tests/host_test/sdkconfig.ci.console", "size": 114, "hash": "36fbd4c9edc43321f412b6704d3b1ad7ad625cf8e12a6881b1ffd09ed8d6fac2"}, {"path": "tests/host_test/CMakeLists.txt", "size": 541, "hash": "d69a08646eb7af5535b1b4fbd78501eb1e8a18086e7db058677139e540fc9440"}, {"path": "tests/host_test/sdkconfig.ci.app", "size": 127, "hash": "b1a7f50d173e21fcc01d3d45e2698e8840c87d6963e2dbb27e6c5943b9125593"}, {"path": "tests/host_test/sdkconfig.ci.target", "size": 26, "hash": "b5cb63deea9b1e4d616752e09fab4ed18fbb039e5f980ea26431acf1307d67cb"}, {"path": "tests/host_test/README.md", "size": 975, "hash": "168820c132bd2c276d22856e5dd533cdc0add8952da06358e19528d4a3dea3a7"}, {"path": "tests/host_test/pytest_mdns.py", "size": 8953, "hash": "8743912240e75b85b358d8da0124ed0dcd0f931e83926c9c5cf0c73a6cc5d0de"}, {"path": "tests/host_test/dnsfixture.py", "size": 4748, "hash": "9328082027347b7d2d9ff19b312b0bbc24a96d77d21ee4b627c19f691de78075"}, {"path": "tests/host_test/sdkconfig.defaults", "size": 199, "hash": "68e2a590541166d099f182e88bf4e239c259cd79bdf516ce1d08f5a4266e7bf8"}, {"path": "tests/host_test/main/CMakeLists.txt", "size": 147, "hash": "750a466f2ab682e1aff3eea846752dc5148b7e638b14ad6842bae79d96e279c5"}, {"path": "tests/host_test/main/idf_component.yml", "size": 201, "hash": "ba446b66e793f1e609b753740c205d1cf9a7b1bad509bdbec6ef694c8401ecf5"}, {"path": "tests/host_test/main/main.c", "size": 3653, "hash": "d5b572af8b345465ff5d4069fd4da02b274d3a6cf2eabd3d33b27aadf9ca9bc9"}, {"path": "tests/host_test/main/Kconfig.projbuild", "size": 507, "hash": "43e5370c092dcace9d05489494f8178df02adace71014f025cc465a01e2e8781"}, {"path": "tests/host_test/components/esp_netif_linux/CMakeLists.txt", "size": 314, "hash": "b57723ef05c094444d19dc123ba11aee2a5693d7685c0a5a9aaf7dfaeee7110b"}, {"path": "tests/host_test/components/esp_netif_linux/esp_netif_linux.c", "size": 4982, "hash": "0ead2caefe0a8b66196b37236724cb85df69f34ccf14d530fe548ff9d5094aee"}, {"path": "tests/host_test/components/esp_netif_linux/Kconfig", "size": 257, "hash": "ac8bff8c86c8e268c11d1926ed0d70459156e00f48d9f715b856b9dbe8d9511d"}, {"path": "tests/host_test/components/esp_netif_linux/include/esp_wifi_types.h", "size": 158, "hash": "42b1dd10e6b6a54bc6f2a3f761d096bd5bbdc7c3d0ba122110fbcd879221bd86"}, {"path": "tests/host_test/components/esp_netif_linux/include/machine/endian.h", "size": 161, "hash": "6affb826a6caad86367e44a69e3ea901b19e6589120980ac73e31f076b782610"}, {"path": "tests/test_afl_fuzz_host/in/file2.bin", "size": 33, "hash": "63e175d3f16aa129587f4bfed0312cff6c3e3bbb95919e0822ea3594fbcfbe30"}, {"path": "tests/test_afl_fuzz_host/in/test-31.bin", "size": 91, "hash": "66a6a82ebd7c30221c0286c4f3126912f64dc710c1a903ef6c86ec2b285d986f"}, {"path": "tests/test_afl_fuzz_host/in/minif_query.bin", "size": 77, "hash": "58c5ae8d824f38612477aa9202394cfaa17bbe562e516871350073c11ad5a61b"}, {"path": "tests/test_afl_fuzz_host/in/minif_any.bin", "size": 109, "hash": "9cc25f3e4ebbaf4191a65397e441993ffed7858c7bdb181d9f0fd5c9cdc8ee8e"}, {"path": "tests/test_afl_fuzz_host/in/telnet_ptr.bin", "size": 72, "hash": "4d95f91cfcf550df0031b06661eae5b253333a2fb1b6aaa0378d3faab82b67ac"}, {"path": "tests/test_afl_fuzz_host/in/test-53.bin", "size": 140, "hash": "613366d3af58645f0315354e7860b7c59bff935f270086145456eadd65e1b2ee"}, {"path": "tests/test_afl_fuzz_host/in/test-83.bin", "size": 105, "hash": "7a6a22d37b7baf95e54eeca95e9684c6742e4234b78e762f5504f3a1451cf621"}, {"path": "tests/test_afl_fuzz_host/in/test-96.bin", "size": 319, "hash": "e44cfbb29156d68f011437451c8e70ff8464f014ede1c52a00ee4b279f9cda99"}, {"path": "tests/test_afl_fuzz_host/in/minif_query2.bin", "size": 64, "hash": "5c1f4e91f3636220b4e12be8b1909aa0af2b387f271b159af86d625d9eb6fc8f"}, {"path": "tests/test_afl_fuzz_host/in/test-56.bin", "size": 262, "hash": "640d1eefd491c61942c490b8f284fa991868fd07ecadeeeee9c2b230866b21fc"}, {"path": "tests/test_afl_fuzz_host/in/test-95.bin", "size": 286, "hash": "21d5fcd95b26331cca324a0e74a0108d76c006ca699d81c5856f7f3904c71063"}, {"path": "tests/test_afl_fuzz_host/in/sub_fritz_m.bin", "size": 72, "hash": "1567d0f345d1341fb12aa95c2c6f7a0cb33d43c0b1ba3aeee462ac61a91b8da7"}, {"path": "tests/test_afl_fuzz_host/in/minif_disc.bin", "size": 65, "hash": "455b13bcbd89c8174a7f627c4f5b8fd1ee36df438790d7f27d5d5b3d07560581"}, {"path": "tests/test_afl_fuzz_host/in/test-89.bin", "size": 459, "hash": "5fd5cbdc1182b10809f4f0fec583233c0d005e6290ceaf24ff32b711537fe64b"}, {"path": "tests/test_afl_fuzz_host/in/test-63.bin", "size": 147, "hash": "ef363f872866047a2a5ce2efad05c828d1bd02c197fc16ee967d05147f1c8f33"}, {"path": "tests/test_afl_fuzz_host/in/test-88.bin", "size": 48, "hash": "2a47f8315ed907b68a8f2d3b19615fad74b2a65db0b9f8f0a70728b3104ce251"}, {"path": "tests/test_afl_fuzz_host/in/minif_4a_txt.bin", "size": 39, "hash": "96f99c6fd77af3634e2329e5274f773fded84c4dec6b83ede92ee8ab9f5c5ecc"}, {"path": "tests/test_afl_fuzz_host/in/minif_ptr.bin", "size": 36, "hash": "d2d174000cbf7fb0e0cb04e05d7b8a7feb3ddad3a8eb3cc666ab83e488fe5e0b"}, {"path": "tests/test_afl_fuzz_host/in/test-16.bin", "size": 254, "hash": "1bd402094cd9ed875ad851b6120dbdf02ae9f347b8d43105f60215557f59970b"}, {"path": "tests/test_afl_fuzz_host/in/minif_aaaa.bin", "size": 33, "hash": "c5d7283539a8f5d976651cdf1e226ab1f2bf4b72249413d8bbcf9316f77adad3"}, {"path": "tests/test_afl_fuzz_host/in/test-29.bin", "size": 39, "hash": "2d1fac83bb6506cad039e3dd17a997e8e1626aaed478063b33c272bbc625e503"}, {"path": "tests/test_afl_fuzz_host/in/test-15.bin", "size": 524, "hash": "22010b88f143fbba4367b697c617ca7999a37fa1eadc8912fb21f4ecabce3258"}, {"path": "tests/test_afl_fuzz_host/in/test-14.bin", "size": 568, "hash": "851633823bf7fa62bcbc81850176d303834100341a1887f8209a7285b977e599"}, {"path": "tests/test_afl_fuzz_host/in/test-28.bin", "size": 62, "hash": "6c29e7d7c377f51d13f2afedac22de8f49f8c252f6cac576b2842896bef1b59f"}, {"path": "tests/test_apps/main/CMakeLists.txt", "size": 93, "hash": "da39a012d77ed08c2115f9365fb14c6e75c694fa73b5bafc1351f6600593c390"}, {"path": "tests/test_apps/main/mdns_test.c", "size": 5793, "hash": "493dba6479833d997ca2cc79cc0b8e48c516303362f71dbfa40608c635904fb0"}, {"path": "tests/test_apps/main/idf_component.yml", "size": 228, "hash": "5115862e7e8d2ace9eb87e34edc0be447a6bce021b579eb79bd45fadfe2740c0"}, {"path": "tests/test_apps/main/main.c", "size": 3423, "hash": "d889f6a7dd56fd90812327554ee68e86780a617dfe2737d5540dfecc300c4524"}, {"path": "tests/test_apps/main/Kconfig.projbuild", "size": 1290, "hash": "8e68cdd831c047ecccb4a0c20fbf2da283590fc17f2abee7afc0ef5d8f4cadc4"}, {"path": "tests/unit_test/main/CMakeLists.txt", "size": 175, "hash": "45f26ae5395a211f0499ef4dac97d1fc1ddccab75e0b2bd672aade8c170ada45"}, {"path": "tests/unit_test/main/test_mdns.c", "size": 14905, "hash": "345b9ddd4bb522b4b3bce79bb11c21ffd6cb4ba86fba4976ae3dba8987b7b628"}]}