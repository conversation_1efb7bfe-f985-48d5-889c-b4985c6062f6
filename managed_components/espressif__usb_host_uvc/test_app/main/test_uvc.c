/*
 * SPDX-FileCopyrightText: 2015-2024 Espressif Systems (Shanghai) CO LTD
 *
 * SPDX-License-Identifier: Apache-2.0
 */

#include "soc/soc_caps.h"
#if SOC_USB_OTG_SUPPORTED

#include <stdio.h>
#include <string.h>
#include "esp_system.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "esp_log.h"
#include "esp_err.h"

#include "esp_private/usb_phy.h"
#include "usb/usb_host.h"
#include "unity.h"

#include "libusb.h"
#include "descriptor.h"

#define TAG "UVC_TEST"
#define COMPARE_DESCRIPTORS(array) compare_descriptors(array, sizeof(array)/sizeof(array[0]), #array)

int libusb_parse_configuration(struct libusb_config_descriptor *config, const uint8_t *buffer, int size);
void libusb_clear_config_descriptor(struct libusb_config_descriptor *config);

// idf_component_register(WHOLE_ARCHIVE) backward compatibility to IDF_v4.4
void linker_hook(void) {};

const uint8_t CANYON_CNE_CWC2[] = {
    0x09, 0x02, 0x7d, 0x02, 0x04, 0x01, 0x00, 0x80, 0xfa, 0x08, 0x0b, 0x00, 0x02, 0x0e, 0x03, 0x00,
    0x05, 0x09, 0x04, 0x00, 0x00, 0x01, 0x0e, 0x01, 0x00, 0x05, 0x0d, 0x24, 0x01, 0x00, 0x01, 0x4d,
    0x00, 0xc0, 0xe1, 0xe4, 0x00, 0x01, 0x01, 0x09, 0x24, 0x03, 0x02, 0x01, 0x01, 0x00, 0x04, 0x00,
    0x1a, 0x24, 0x06, 0x04, 0x70, 0x33, 0xf0, 0x28, 0x11, 0x63, 0x2e, 0x4a, 0xba, 0x2c, 0x68, 0x90,
    0xeb, 0x33, 0x40, 0x16, 0x08, 0x01, 0x03, 0x01, 0x0f, 0x00, 0x12, 0x24, 0x02, 0x01, 0x01, 0x02,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x0e, 0x20, 0x00, 0x0b, 0x24, 0x05, 0x03,
    0x01, 0x00, 0x00, 0x02, 0x3f, 0x04, 0x00, 0x07, 0x05, 0x83, 0x03, 0x10, 0x00, 0x06, 0x05, 0x25,
    0x03, 0x10, 0x00, 0x09, 0x04, 0x01, 0x00, 0x00, 0x0e, 0x02, 0x00, 0x05, 0x0e, 0x24, 0x01, 0x01,
    0x33, 0x01, 0x81, 0x00, 0x02, 0x02, 0x01, 0x01, 0x01, 0x00, 0x0b, 0x24, 0x06, 0x01, 0x05, 0x00,
    0x01, 0x00, 0x00, 0x00, 0x00, 0x32, 0x24, 0x07, 0x01, 0x00, 0x80, 0x02, 0xe0, 0x01, 0x00, 0x00,
    0x77, 0x01, 0x00, 0x00, 0xca, 0x08, 0x00, 0x60, 0x09, 0x00, 0x15, 0x16, 0x05, 0x00, 0x06, 0x15,
    0x16, 0x05, 0x00, 0x80, 0x1a, 0x06, 0x00, 0x20, 0xa1, 0x07, 0x00, 0x2a, 0x2c, 0x0a, 0x00, 0x40,
    0x42, 0x0f, 0x00, 0x80, 0x84, 0x1e, 0x00, 0x32, 0x24, 0x07, 0x02, 0x00, 0x60, 0x01, 0x20, 0x01,
    0x00, 0xc0, 0x7b, 0x00, 0x00, 0x80, 0xe6, 0x02, 0x00, 0x18, 0x03, 0x00, 0x15, 0x16, 0x05, 0x00,
    0x06, 0x15, 0x16, 0x05, 0x00, 0x80, 0x1a, 0x06, 0x00, 0x20, 0xa1, 0x07, 0x00, 0x2a, 0x2c, 0x0a,
    0x00, 0x40, 0x42, 0x0f, 0x00, 0x80, 0x84, 0x1e, 0x00, 0x32, 0x24, 0x07, 0x03, 0x00, 0x40, 0x01,
    0xf0, 0x00, 0x00, 0xc0, 0x5d, 0x00, 0x00, 0x80, 0x32, 0x02, 0x00, 0x58, 0x02, 0x00, 0x15, 0x16,
    0x05, 0x00, 0x06, 0x15, 0x16, 0x05, 0x00, 0x80, 0x1a, 0x06, 0x00, 0x20, 0xa1, 0x07, 0x00, 0x2a,
    0x2c, 0x0a, 0x00, 0x40, 0x42, 0x0f, 0x00, 0x80, 0x84, 0x1e, 0x00, 0x32, 0x24, 0x07, 0x04, 0x00,
    0xb0, 0x00, 0x90, 0x00, 0x00, 0xf0, 0x1e, 0x00, 0x00, 0xa0, 0xb9, 0x00, 0x00, 0xc6, 0x00, 0x00,
    0x15, 0x16, 0x05, 0x00, 0x06, 0x15, 0x16, 0x05, 0x00, 0x80, 0x1a, 0x06, 0x00, 0x20, 0xa1, 0x07,
    0x00, 0x2a, 0x2c, 0x0a, 0x00, 0x40, 0x42, 0x0f, 0x00, 0x80, 0x84, 0x1e, 0x00, 0x32, 0x24, 0x07,
    0x05, 0x00, 0xa0, 0x00, 0x78, 0x00, 0x00, 0x70, 0x17, 0x00, 0x00, 0xa0, 0x8c, 0x00, 0x00, 0x96,
    0x00, 0x00, 0x15, 0x16, 0x05, 0x00, 0x06, 0x15, 0x16, 0x05, 0x00, 0x80, 0x1a, 0x06, 0x00, 0x20,
    0xa1, 0x07, 0x00, 0x2a, 0x2c, 0x0a, 0x00, 0x40, 0x42, 0x0f, 0x00, 0x80, 0x84, 0x1e, 0x00, 0x1a,
    0x24, 0x03, 0x00, 0x05, 0x80, 0x02, 0xe0, 0x01, 0x60, 0x01, 0x20, 0x01, 0x40, 0x01, 0xf0, 0x00,
    0xb0, 0x00, 0x90, 0x00, 0xa0, 0x00, 0x78, 0x00, 0x00, 0x06, 0x24, 0x0d, 0x01, 0x01, 0x04, 0x09,
    0x04, 0x01, 0x01, 0x01, 0x0e, 0x02, 0x00, 0x00, 0x07, 0x05, 0x81, 0x05, 0x80, 0x00, 0x01, 0x09,
    0x04, 0x01, 0x02, 0x01, 0x0e, 0x02, 0x00, 0x00, 0x07, 0x05, 0x81, 0x05, 0x00, 0x01, 0x01, 0x09,
    0x04, 0x01, 0x03, 0x01, 0x0e, 0x02, 0x00, 0x00, 0x07, 0x05, 0x81, 0x05, 0x00, 0x02, 0x01, 0x09,
    0x04, 0x01, 0x04, 0x01, 0x0e, 0x02, 0x00, 0x00, 0x07, 0x05, 0x81, 0x05, 0x58, 0x02, 0x01, 0x09,
    0x04, 0x01, 0x05, 0x01, 0x0e, 0x02, 0x00, 0x00, 0x07, 0x05, 0x81, 0x05, 0x20, 0x03, 0x01, 0x09,
    0x04, 0x01, 0x06, 0x01, 0x0e, 0x02, 0x00, 0x00, 0x07, 0x05, 0x81, 0x05, 0xbc, 0x03, 0x01, 0x08,
    0x0b, 0x02, 0x02, 0x01, 0x00, 0x00, 0x04, 0x09, 0x04, 0x02, 0x00, 0x00, 0x01, 0x01, 0x00, 0x04,
    0x09, 0x24, 0x01, 0x00, 0x01, 0x29, 0x00, 0x01, 0x03, 0x0c, 0x24, 0x02, 0x01, 0x01, 0x02, 0x00,
    0x01, 0x00, 0x00, 0x00, 0x00, 0x0b, 0x24, 0x06, 0x02, 0x01, 0x02, 0x01, 0x00, 0x02, 0x00, 0x00,
    0x09, 0x24, 0x03, 0x03, 0x01, 0x01, 0x00, 0x02, 0x00, 0x09, 0x04, 0x03, 0x00, 0x00, 0x01, 0x02,
    0x00, 0x00, 0x09, 0x04, 0x03, 0x01, 0x01, 0x01, 0x02, 0x00, 0x00, 0x07, 0x24, 0x01, 0x03, 0x01,
    0x01, 0x00, 0x0b, 0x24, 0x02, 0x01, 0x01, 0x02, 0x10, 0x01, 0x80, 0x3e, 0x00, 0x09, 0x05, 0x84,
    0x05, 0x20, 0x00, 0x04, 0x00, 0x00, 0x07, 0x25, 0x01, 0x01, 0x00, 0x00, 0x00,
};

const uint8_t Logitech_C980[] = {
    0x09, 0x02, 0x5a, 0x03, 0x05, 0x01, 0x00, 0x80, 0xfa, 0x08, 0x0b, 0x00, 0x02, 0x0e, 0x03, 0x00,
    0x00, 0x09, 0x04, 0x00, 0x00, 0x01, 0x0e, 0x01, 0x00, 0x00, 0x0d, 0x24, 0x01, 0x00, 0x01, 0xd8,
    0x00, 0x80, 0xc3, 0xc9, 0x01, 0x01, 0x01, 0x12, 0x24, 0x02, 0x01, 0x01, 0x02, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x2e, 0x2a, 0x02, 0x0b, 0x24, 0x05, 0x03, 0x01, 0x00, 0x40,
    0x02, 0x5b, 0x17, 0x00, 0x1b, 0x24, 0x06, 0x0e, 0x6a, 0xd1, 0x49, 0x2c, 0xb8, 0x32, 0x85, 0x44,
    0x3e, 0xa8, 0x64, 0x3a, 0x15, 0x23, 0x62, 0xf2, 0x06, 0x01, 0x03, 0x02, 0x3f, 0x00, 0x00, 0x1b,
    0x24, 0x06, 0x06, 0xd0, 0x9e, 0xe4, 0x23, 0x78, 0x11, 0x31, 0x4f, 0xae, 0x52, 0xd2, 0xfb, 0x8a,
    0x8d, 0x3b, 0x48, 0x0e, 0x01, 0x03, 0x02, 0xff, 0x6f, 0x00, 0x1b, 0x24, 0x06, 0x08, 0xe4, 0x8e,
    0x67, 0x69, 0x0f, 0x41, 0xdb, 0x40, 0xa8, 0x50, 0x74, 0x20, 0xd7, 0xd8, 0x24, 0x0e, 0x08, 0x01,
    0x03, 0x02, 0x3f, 0x0f, 0x00, 0x1c, 0x24, 0x06, 0x09, 0xa9, 0x4c, 0x5d, 0x1f, 0x11, 0xde, 0x87,
    0x44, 0x84, 0x0d, 0x50, 0x93, 0x3c, 0x8e, 0xc8, 0xd1, 0x12, 0x01, 0x03, 0x03, 0xff, 0xff, 0x03,
    0x00, 0x1c, 0x24, 0x06, 0x0a, 0x15, 0x02, 0xe4, 0x49, 0x34, 0xf4, 0xfe, 0x47, 0xb1, 0x58, 0x0e,
    0x88, 0x50, 0x23, 0xe5, 0x1b, 0x0b, 0x01, 0x03, 0x03, 0xfa, 0xff, 0x01, 0x00, 0x1c, 0x24, 0x06,
    0x0b, 0x21, 0x2d, 0xe5, 0xff, 0x30, 0x80, 0x2c, 0x4e, 0x82, 0xd9, 0xf5, 0x87, 0xd0, 0x05, 0x40,
    0xbd, 0x04, 0x01, 0x03, 0x03, 0x00, 0x41, 0x01, 0x00, 0x09, 0x24, 0x03, 0x04, 0x01, 0x01, 0x00,
    0x03, 0x00, 0x07, 0x05, 0x85, 0x03, 0x40, 0x00, 0x10, 0x05, 0x25, 0x03, 0x40, 0x00, 0x09, 0x04,
    0x01, 0x00, 0x00, 0x0e, 0x02, 0x00, 0x00, 0x0f, 0x24, 0x01, 0x02, 0x79, 0x01, 0x81, 0x00, 0x04,
    0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x1b, 0x24, 0x04, 0x01, 0x01, 0x59, 0x55, 0x59, 0x32, 0x00,
    0x00, 0x10, 0x00, 0x80, 0x00, 0x00, 0xaa, 0x00, 0x38, 0x9b, 0x71, 0x10, 0x01, 0x00, 0x00, 0x00,
    0x00, 0x2a, 0x24, 0x05, 0x01, 0x00, 0xb0, 0x00, 0x90, 0x00, 0x00, 0xf0, 0x1e, 0x00, 0x00, 0xd0,
    0x5c, 0x00, 0x00, 0xc6, 0x00, 0x00, 0x2a, 0x2c, 0x0a, 0x00, 0x04, 0x2a, 0x2c, 0x0a, 0x00, 0x40,
    0x42, 0x0f, 0x00, 0x55, 0x58, 0x14, 0x00, 0x80, 0x84, 0x1e, 0x00, 0x06, 0x24, 0x0d, 0x01, 0x01,
    0x04, 0x0b, 0x24, 0x06, 0x02, 0x05, 0x01, 0x01, 0x00, 0x00, 0x00, 0x00, 0x36, 0x24, 0x07, 0x01,
    0x00, 0x80, 0x02, 0xe0, 0x01, 0x00, 0x00, 0x77, 0x01, 0x00, 0x00, 0xca, 0x08, 0x00, 0x60, 0x09,
    0x00, 0x15, 0x16, 0x05, 0x00, 0x07, 0x15, 0x16, 0x05, 0x00, 0x9a, 0x5b, 0x06, 0x00, 0x20, 0xa1,
    0x07, 0x00, 0x2a, 0x2c, 0x0a, 0x00, 0x40, 0x42, 0x0f, 0x00, 0x55, 0x58, 0x14, 0x00, 0x80, 0x84,
    0x1e, 0x00, 0x36, 0x24, 0x07, 0x02, 0x00, 0xb0, 0x00, 0x90, 0x00, 0x00, 0xf0, 0x1e, 0x00, 0x00,
    0xa0, 0xb9, 0x00, 0x00, 0xc6, 0x00, 0x00, 0x15, 0x16, 0x05, 0x00, 0x07, 0x15, 0x16, 0x05, 0x00,
    0x9a, 0x5b, 0x06, 0x00, 0x20, 0xa1, 0x07, 0x00, 0x2a, 0x2c, 0x0a, 0x00, 0x40, 0x42, 0x0f, 0x00,
    0x55, 0x58, 0x14, 0x00, 0x80, 0x84, 0x1e, 0x00, 0x36, 0x24, 0x07, 0x03, 0x00, 0x40, 0x01, 0xf0,
    0x00, 0x00, 0xc0, 0x5d, 0x00, 0x00, 0x80, 0x32, 0x02, 0x00, 0x58, 0x02, 0x00, 0x15, 0x16, 0x05,
    0x00, 0x07, 0x15, 0x16, 0x05, 0x00, 0x9a, 0x5b, 0x06, 0x00, 0x20, 0xa1, 0x07, 0x00, 0x2a, 0x2c,
    0x0a, 0x00, 0x40, 0x42, 0x0f, 0x00, 0x55, 0x58, 0x14, 0x00, 0x80, 0x84, 0x1e, 0x00, 0x36, 0x24,
    0x07, 0x04, 0x00, 0xa8, 0x01, 0xf0, 0x00, 0x00, 0x38, 0x7c, 0x00, 0x00, 0x50, 0xe9, 0x02, 0x00,
    0x1b, 0x03, 0x00, 0x15, 0x16, 0x05, 0x00, 0x07, 0x15, 0x16, 0x05, 0x00, 0x9a, 0x5b, 0x06, 0x00,
    0x20, 0xa1, 0x07, 0x00, 0x2a, 0x2c, 0x0a, 0x00, 0x40, 0x42, 0x0f, 0x00, 0x55, 0x58, 0x14, 0x00,
    0x80, 0x84, 0x1e, 0x00, 0x36, 0x24, 0x07, 0x05, 0x00, 0x80, 0x02, 0x68, 0x01, 0x00, 0x40, 0x19,
    0x01, 0x00, 0x80, 0x97, 0x06, 0x00, 0x08, 0x07, 0x00, 0x15, 0x16, 0x05, 0x00, 0x07, 0x15, 0x16,
    0x05, 0x00, 0x9a, 0x5b, 0x06, 0x00, 0x20, 0xa1, 0x07, 0x00, 0x2a, 0x2c, 0x0a, 0x00, 0x40, 0x42,
    0x0f, 0x00, 0x55, 0x58, 0x14, 0x00, 0x80, 0x84, 0x1e, 0x00, 0x06, 0x24, 0x0d, 0x01, 0x01, 0x04,
    0x09, 0x04, 0x01, 0x01, 0x01, 0x0e, 0x02, 0x00, 0x00, 0x07, 0x05, 0x81, 0x05, 0xc0, 0x00, 0x01,
    0x09, 0x04, 0x01, 0x02, 0x01, 0x0e, 0x02, 0x00, 0x00, 0x07, 0x05, 0x81, 0x05, 0x80, 0x01, 0x01,
    0x09, 0x04, 0x01, 0x03, 0x01, 0x0e, 0x02, 0x00, 0x00, 0x07, 0x05, 0x81, 0x05, 0x00, 0x02, 0x01,
    0x09, 0x04, 0x01, 0x04, 0x01, 0x0e, 0x02, 0x00, 0x00, 0x07, 0x05, 0x81, 0x05, 0x80, 0x02, 0x01,
    0x09, 0x04, 0x01, 0x05, 0x01, 0x0e, 0x02, 0x00, 0x00, 0x07, 0x05, 0x81, 0x05, 0x20, 0x03, 0x01,
    0x08, 0x0b, 0x02, 0x02, 0x01, 0x02, 0x00, 0x00, 0x09, 0x04, 0x02, 0x00, 0x00, 0x01, 0x01, 0x00,
    0x00, 0x09, 0x24, 0x01, 0x00, 0x01, 0x26, 0x00, 0x01, 0x03, 0x0c, 0x24, 0x02, 0x01, 0x01, 0x02,
    0x00, 0x02, 0x03, 0x00, 0x00, 0x00, 0x09, 0x24, 0x03, 0x03, 0x01, 0x01, 0x00, 0x05, 0x00, 0x08,
    0x24, 0x06, 0x05, 0x01, 0x01, 0x03, 0x00, 0x09, 0x04, 0x03, 0x00, 0x00, 0x01, 0x02, 0x00, 0x00,
    0x09, 0x04, 0x03, 0x01, 0x01, 0x01, 0x02, 0x00, 0x00, 0x07, 0x24, 0x01, 0x03, 0x01, 0x01, 0x00,
    0x0b, 0x24, 0x02, 0x01, 0x02, 0x02, 0x10, 0x01, 0x80, 0x3e, 0x00, 0x09, 0x05, 0x84, 0x05, 0x44,
    0x00, 0x04, 0x00, 0x00, 0x07, 0x25, 0x01, 0x01, 0x00, 0x00, 0x00, 0x09, 0x04, 0x04, 0x00, 0x01,
    0x03, 0x00, 0x00, 0x00, 0x09, 0x21, 0x11, 0x01, 0x00, 0x01, 0x22, 0x6c, 0x00, 0x07, 0x05, 0x87,
    0x03, 0x02, 0x00, 0x06, 0x06, 0x30, 0x00, 0x00, 0x02, 0x00
};

const uint8_t unknown_camera[] = {
    0x09, 0x02, 0x69, 0x01, 0x02, 0x01, 0x00, 0x80, 0xfa, 0x08, 0x0b, 0x00, 0x02, 0x0e, 0x03, 0x00,
    0x05, 0x09, 0x04, 0x00, 0x00, 0x01, 0x0e, 0x01, 0x00, 0x05, 0x0d, 0x24, 0x01, 0x00, 0x01, 0x4d,
    0x00, 0xc0, 0xe1, 0xe4, 0x00, 0x01, 0x01, 0x09, 0x24, 0x03, 0x05, 0x01, 0x01, 0x00, 0x03, 0x00,
    0x1a, 0x24, 0x06, 0x03, 0x70, 0x33, 0xf0, 0x28, 0x11, 0x63, 0x2e, 0x4a, 0xba, 0x2c, 0x68, 0x90,
    0xeb, 0x33, 0x40, 0x16, 0x08, 0x01, 0x02, 0x01, 0x1f, 0x00, 0x12, 0x24, 0x02, 0x01, 0x01, 0x02,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x0e, 0x00, 0x00, 0x0b, 0x24, 0x05, 0x02,
    0x01, 0x00, 0x00, 0x02, 0x7f, 0x17, 0x00, 0x07, 0x05, 0x83, 0x03, 0x10, 0x00, 0x06, 0x05, 0x25,
    0x03, 0x10, 0x00, 0x09, 0x04, 0x01, 0x00, 0x00, 0x0e, 0x02, 0x00, 0x05, 0x0f, 0x24, 0x01, 0x02,
    0x8d, 0x00, 0x81, 0x00, 0x05, 0x00, 0x01, 0x01, 0x01, 0x00, 0x00, 0x0b, 0x24, 0x06, 0x01, 0x01,
    0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x2e, 0x24, 0x07, 0x01, 0x00, 0x40, 0x01, 0xf0, 0x00, 0x08,
    0x3c, 0x2f, 0x00, 0x30, 0x68, 0x1b, 0x01, 0x4d, 0x2e, 0x01, 0x00, 0x15, 0x16, 0x05, 0x00, 0x05,
    0x15, 0x16, 0x05, 0x00, 0x20, 0xa1, 0x07, 0x00, 0x2a, 0x2c, 0x0a, 0x00, 0x40, 0x42, 0x0f, 0x00,
    0x80, 0x84, 0x1e, 0x00, 0x06, 0x24, 0x0d, 0x01, 0x01, 0x04, 0x1b, 0x24, 0x04, 0x02, 0x01, 0x59,
    0x55, 0x59, 0x32, 0x00, 0x00, 0x10, 0x00, 0x80, 0x00, 0x00, 0xaa, 0x00, 0x38, 0x9b, 0x71, 0x10,
    0x01, 0x00, 0x00, 0x00, 0x00, 0x1e, 0x24, 0x05, 0x01, 0x00, 0x40, 0x01, 0xf0, 0x00, 0x00, 0xc0,
    0x5d, 0x00, 0x00, 0xc0, 0x5d, 0x00, 0x00, 0x58, 0x02, 0x00, 0x80, 0x84, 0x1e, 0x00, 0x01, 0x80,
    0x84, 0x1e, 0x00, 0x06, 0x24, 0x0d, 0x01, 0x01, 0x04, 0x09, 0x04, 0x01, 0x01, 0x01, 0x0e, 0x02,
    0x00, 0x00, 0x07, 0x05, 0x81, 0x05, 0x80, 0x00, 0x01, 0x09, 0x04, 0x01, 0x02, 0x01, 0x0e, 0x02,
    0x00, 0x00, 0x07, 0x05, 0x81, 0x05, 0x00, 0x01, 0x01, 0x09, 0x04, 0x01, 0x03, 0x01, 0x0e, 0x02,
    0x00, 0x00, 0x07, 0x05, 0x81, 0x05, 0x00, 0x02, 0x01, 0x09, 0x04, 0x01, 0x04, 0x01, 0x0e, 0x02,
    0x00, 0x00, 0x07, 0x05, 0x81, 0x05, 0x58, 0x02, 0x01, 0x09, 0x04, 0x01, 0x05, 0x01, 0x0e, 0x02,
    0x00, 0x00, 0x07, 0x05, 0x81, 0x05, 0x20, 0x03, 0x01, 0x09, 0x04, 0x01, 0x06, 0x01, 0x0e, 0x02,
    0x00, 0x00, 0x07, 0x05, 0x81, 0x05, 0xbc, 0x03, 0x01
};

static void compare_descriptors(const uint8_t *config_desc, size_t size, const char *camera_name)
{
    ESP_LOGI(TAG, "Comparing descriptrors for %s camera", camera_name);

    struct libusb_config_descriptor *libusb_config = calloc(1, sizeof(struct libusb_config_descriptor));
    struct libusb_config_descriptor *config = NULL;

    TEST_ASSERT_NOT_NULL( libusb_config );
    TEST_ASSERT_EQUAL( libusb_parse_configuration(libusb_config, config_desc, size), LIBUSB_SUCCESS );
    TEST_ASSERT_EQUAL( raw_desc_to_libusb_config(config_desc, size, &config), LIBUSB_SUCCESS );

    TEST_ASSERT_EQUAL( memcmp(libusb_config, config, LIBUSB_DT_CONFIG_SIZE), 0);
    TEST_ASSERT_NOT_NULL( libusb_config->interface );
    TEST_ASSERT_NOT_NULL( config->interface );

    TEST_ASSERT_EQUAL( libusb_config->extra_length, config->extra_length );
    TEST_ASSERT_EQUAL( memcmp(libusb_config->extra, config->extra, config->extra_length), 0);

    TEST_ASSERT_EQUAL( libusb_config->bNumInterfaces, config->bNumInterfaces );

    printf("checking...\n");

    for (int i = 0; i < libusb_config->bNumInterfaces; i++) {
        printf("interface %u\n", i);
        libusb_interface_t *ifc = &libusb_config->interface[i];
        libusb_interface_t *my_ifc = &config->interface[i];
        TEST_ASSERT_EQUAL( ifc->num_altsetting, my_ifc->num_altsetting );

        for (int j = 0; j < ifc->num_altsetting; j++) {
            printf("  altsetting %u\n", j);
            libusb_interface_descriptor_t *libusb_alt = &ifc->altsetting[j];
            libusb_interface_descriptor_t *alt = &my_ifc->altsetting[j];
            TEST_ASSERT_EQUAL( memcmp(libusb_alt, alt, LIBUSB_DT_INTERFACE_SIZE), 0);
            TEST_ASSERT_EQUAL( libusb_alt->extra_length, alt->extra_length );
            TEST_ASSERT_EQUAL( memcmp(libusb_alt->extra, alt->extra, alt->extra_length), 0);
            TEST_ASSERT_EQUAL( libusb_alt->bNumEndpoints, alt->bNumEndpoints );

            for (int ep = 0; ep < libusb_alt->bNumEndpoints; ep++) {
                printf("    endpoint %u\n", ep);
                libusb_endpoint_descriptor_t *libusb_endpoint = &libusb_alt->endpoint[ep];
                libusb_endpoint_descriptor_t *endpoint = &alt->endpoint[ep];
                TEST_ASSERT_EQUAL( memcmp(libusb_endpoint, endpoint, LIBUSB_DT_ENDPOINT_SIZE), 0);
                TEST_ASSERT_EQUAL( libusb_endpoint->extra_length, endpoint->extra_length );
                TEST_ASSERT_EQUAL( memcmp(libusb_endpoint->extra, endpoint->extra, endpoint->extra_length), 0);
            }
        }
    }

    libusb_clear_config_descriptor(libusb_config);
    clear_config_descriptor(config);
}

// Test compares config descriptor created by usb_host_uvc driver and originally used libusb function
TEST_CASE("Compare config descriptor parser", "[usb_uvc]")
{
    COMPARE_DESCRIPTORS(CANYON_CNE_CWC2);
    COMPARE_DESCRIPTORS(Logitech_C980);
    COMPARE_DESCRIPTORS(unknown_camera);
}

#endif
