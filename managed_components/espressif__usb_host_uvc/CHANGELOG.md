## 1.0.4

- Support printf frame based descriptor

## 1.0.3

- Added support for ESP32-P4
- Bumped libuvc version to relax frame format negotiation
- Fixed crash on opening non-UVC devices
- Added `libuvc_get_usb_device_info` function

## 1.0.2

- Updated libuvc library to 0.0.7 https://github.com/libuvc/libuvc/tree/v0.0.7
- Added Software BoM information

## 1.0.1

- Fixed compatibility with IDF v4.4

## 1.0.0

- Initial version
