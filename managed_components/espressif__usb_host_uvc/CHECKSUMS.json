{"version": "1.0", "algorithm": "sha256", "created_at": "2025-05-21T15:51:22.245814+00:00", "files": [{"path": "CMakeLists.txt", "size": 1230, "hash": "386796e9a795280d598e1180c17bb1782ad746794c93b08dfd6a9c6a40e1fb85"}, {"path": "CHANGELOG.md", "size": 434, "hash": "84eaac48809a168157148520527f2100231c2955aab1f78a95d54ab4e6912cea"}, {"path": "idf_component.yml", "size": 432, "hash": "e6b7bfa3f08a435b762dcf985cd9436330bfd912b638142fd4c30af52e4ecf6c"}, {"path": "sbom_libuvc.yml", "size": 196, "hash": "37efc1a9fd83caea460f628e3fa670db6c54b6470e11ede78c1ab1b6e90bd807"}, {"path": "README.md", "size": 1761, "hash": "8af2bdf5c7286e9997902eb481efac298021501a8e68c13980d1e53775a64c9e"}, {"path": "LICENCE", "size": 11357, "hash": "c71d239df91726fc519c6eb72d318ec65820627232b2f796219e87dcf35d0ab4"}, {"path": "private_include/descriptor.h", "size": 1093, "hash": "0d77a824ad185c33d32d58324abcb63fa692684c8c930ac0470f55a0fdf21b38"}, {"path": "private_include/libusb.h", "size": 12271, "hash": "00de00b43d111efbe34666e18a4a1c72e6766739d6c133079533258adccc45b6"}, {"path": "test_app/CMakeLists.txt", "size": 512, "hash": "03b0b47bdea9406a689cb5c0494e2ab41548b5e299be9717b2730fce060aa2a0"}, {"path": "test_app/pytest_usb_host_uvc.py", "size": 325, "hash": "99adbe0ac7d791596d61b152551059720851689dc5b0146aa44cda8138c1ca94"}, {"path": "test_app/README.md", "size": 122, "hash": "cfdfcd7d37958e9b46d7feba06bd4216f52daf64bc04765723f1ec2bdb28b21d"}, {"path": "test_app/sdkconfig.defaults", "size": 524, "hash": "950a7290feb1374785ee15edbf8e68d35c9a70546a9c19f86d9a335956b7e524"}, {"path": "libuvc/doxygen.conf", "size": 99424, "hash": "093f3a6698bbf8e118ce438e4017e65a2ae4ef7806d69ea85ca7efc146d7dd80"}, {"path": "libuvc/CMakeLists.txt", "size": 5982, "hash": "90f5ea2897b20c1c1212a37d2c2f97d4522b4f67b2ab9da0e2ce782d107fff06"}, {"path": "libuvc/standard-units.yaml", "size": 13101, "hash": "cb97e20756479e9db58fd17f795476fa8d5647f4e601a922121d7e4200152bee"}, {"path": "libuvc/README.md", "size": 1272, "hash": "5a4439b8480108ba6f2461d06f6d6b3af8476a4dbe8a80ca8460e24cee731124"}, {"path": "libuvc/libuvcConfig.cmake", "size": 473, "hash": "1b9831cd271a0b7bc3c51f15c28229b6c50b83ca04a64d93e459de00003441a3"}, {"path": "libuvc/.gitignore", "size": 48, "hash": "379d5c3a4dbd3cb5db8371d904df17423921cf62af049692413ddd9f1949dfce"}, {"path": "libuvc/.gitattributes", "size": 153, "hash": "ce56810ec9cd4fe15ea612f4254946766f35a77f37753c0ba1a90855d68779d6"}, {"path": "libuvc/changelog.txt", "size": 8555, "hash": "b3ffd4c741d26b6a013c88f9eeba6cbd34090eccbc58500cff486eeaabf05201"}, {"path": "libuvc/LICENSE.txt", "size": 1522, "hash": "57a7ba48475cb7f0aaddfb79ef32e742a19901908f0287b6827da336477b212e"}, {"path": "libuvc/.travis.yml", "size": 187, "hash": "b222d0854743c43539dd6ef222c4fe3f4280b4359af173addd9beb2eb944050e"}, {"path": "libuvc/libuvc.pc.in", "size": 323, "hash": "24fc53e6b05f66589b2e207cbadbe891f650ba09895751a6fa23af62c6d081a2"}, {"path": "include/libuvc_adapter.h", "size": 2176, "hash": "cd0a2ce235d99d21b961dbb61c22389a8f316b563184b010486a90bfe16898b8"}, {"path": "include/libuvc_helper.h", "size": 1292, "hash": "beccd52dded043cf017aa62563242b4935a5a56a90ee9681ed1e95e4492a23aa"}, {"path": "src/libusb_adapter.c", "size": 27340, "hash": "c249d75e6c1e51ad8c1046f231d9d243894754b9b3274f1baca183da036172b9"}, {"path": "src/descriptor.c", "size": 31995, "hash": "9fb0179df216fe607778cf9905c8d7dab2bc750807fc7bb7f700dcde86e62b54"}, {"path": "libuvc/cmake/FindJpegPkg.cmake", "size": 2294, "hash": "23671b4a0497857ae98462dd67af55d873c121150a3c31c448326a752b06eb70"}, {"path": "libuvc/cmake/FindLibUSB.cmake", "size": 1304, "hash": "0d231ab9976fb54ddfb0eff1bbdba67af7e3470a7fcc90ddd52fc939e0ab4d66"}, {"path": "libuvc/cmake/FindOpenCVPkg.cmake", "size": 2432, "hash": "f6632dff28a4856b164f6db9f9908270bb613711d2fbd80ad6e9f0282d3bba92"}, {"path": "libuvc/include/utlist.h", "size": 36984, "hash": "c7a9d29f5c988d2faaa08523df9d4b6a5c71a3dc278b1b1fe2e54b3e8232b403"}, {"path": "libuvc/cameras/isight_imac.txt", "size": 8874, "hash": "81ce8f9d46cab77f5b3fb68fb6430d1d6ebcf91b0f3733295d64820bd41aa110"}, {"path": "libuvc/cameras/isight_macbook.txt", "size": 8874, "hash": "0ccde9ae5d59aed2be6a1b87e3503093a27ab7a9133d6604fc4611079914398f"}, {"path": "libuvc/cameras/quickcampro9000_extra_ctrls.txt", "size": 362, "hash": "48db2659fc2406d53bcfdef90f146a5bf53ff0ca209a51798e75fd4c68f50137"}, {"path": "libuvc/cameras/quickcampro9000.txt", "size": 64478, "hash": "ca3f48cb5812a9d400f32a1c2b026fc9dd77e8fd49ef068a451fa4145245d8cd"}, {"path": "libuvc/cameras/ms_lifecam_show.txt", "size": 31029, "hash": "6d6d8067a4509be5608e1bbcba2f16ded00c82a20dcb88e7e4e0fa3cd061fb4b"}, {"path": "libuvc/cameras/quickcampro9000_builtin_ctrls.txt", "size": 277, "hash": "4f8992b158f7bd6667f1e162a274cc62e22fc6dcf98d792f47d9b9f8dc4c928b"}, {"path": "libuvc/cameras/logitech_hd_pro_920.txt", "size": 77622, "hash": "96959d269100b25672380f94522d4817284ca8549c1203bd182f3b3e65472cbf"}, {"path": "libuvc/src/ctrl-gen.c", "size": 57234, "hash": "b61be787accb87065ab16bdaee5b0326f87972751ebab7b3e4a3a2374b4a79fc"}, {"path": "libuvc/src/frame-mjpeg.c", "size": 8363, "hash": "191cc2c222c6b4d358a5a5ca073ebdd87a4b2c5542337a675a26f12b7d5e7141"}, {"path": "libuvc/src/ctrl.c", "size": 5633, "hash": "d21fb90d2b953d7f9505c234bbd2a08b0eae6ea5741b76433bfca12a0fcb25f1"}, {"path": "libuvc/src/diag.c", "size": 13890, "hash": "d50e79317d1e4fe65ec397357a156d90e266be28377c318b48b6a97360c6e76f"}, {"path": "libuvc/src/init.c", "size": 5316, "hash": "a4d085d05a1b6c3350ee8ca199e27c852caee982679a629bd5c90a65b6c06631"}, {"path": "libuvc/src/ctrl-gen.py", "size": 10263, "hash": "68bb215d74def873dbd6921fef85b60c7b596eb544e23b76f7d17eee6b7f5f43"}, {"path": "libuvc/src/example.c", "size": 6934, "hash": "8c9163f948d21ff46a22a95fe0fa9d9f777216c95ff00535a8fe24797c09dd35"}, {"path": "libuvc/src/device.c", "size": 53820, "hash": "2923f6f4aecace4743ba70c2e0a49805f78126aa61993e35baa53f83d053252f"}, {"path": "libuvc/src/test.c", "size": 4325, "hash": "e238bc36259e578183ea4518d262351c31dc15b689161c889e19ec68ea958d89"}, {"path": "libuvc/src/frame.c", "size": 14013, "hash": "e9340cf694caf67ec842b468e14063d626044cfc9b12f62653905eef348f7df5"}, {"path": "libuvc/src/misc.c", "size": 2043, "hash": "2bb49ea467af99a2c737ebbccbf6c3a1c13a52d308c338a320599514b6f9727e"}, {"path": "libuvc/src/stream.c", "size": 48238, "hash": "a90b0efe1fc07fcca8f39d2961f119a92c07d97c025763612b23b249bb0d040e"}, {"path": "libuvc/include/libuvc/libuvc_config.h.in", "size": 802, "hash": "d42c3b6fc6da5f813ea889dc4a8eabe85edf7d84680d5c6e79ef1e863e1e713c"}, {"path": "libuvc/include/libuvc/libuvc.h", "size": 30519, "hash": "23ba529637f503d1efbde53280abd0f2512d7c333944ae933111653e6217846f"}, {"path": "libuvc/include/libuvc/libuvc_internal.h", "size": 10156, "hash": "fb9d715293c4ca85a62bd0533da36f68c5e2e33ee42c039aaaefb4f2a55d2b7d"}, {"path": "test_app/main/libusb_parse.c", "size": 15173, "hash": "75418740c4438d91844e94ad934ecdee2885900fc0a1aa659ada2aa9161f1334"}, {"path": "test_app/main/CMakeLists.txt", "size": 1170, "hash": "5d245e824cd9121c6ed9f33e98afba54e7e1d1cee285585d1370cc30c23d0906"}, {"path": "test_app/main/test_app_main.c", "size": 2135, "hash": "7523acd84ba8037be90b7b6376f613e5a909ab38c557640d45504e8a6efceebc"}, {"path": "test_app/main/test_uvc.c", "size": 15530, "hash": "45b6d16fd593a12bf0361205a70e276b1bfe327699673af35c0414ba3d6b5b49"}]}