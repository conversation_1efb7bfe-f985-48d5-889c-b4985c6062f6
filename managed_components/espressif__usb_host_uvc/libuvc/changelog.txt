Changes in 0.0.7 (2023-03-31)
----------------

New features:
  - USB 3 support
  - Several image/stream formats (incl. NV12, H264, BGR, RGB, P010)
  - Format conversion functions
  - Accept pre-opened libusb handles (used on Android)
  - Library filename versioning (libuvc.0.0.7.dylib)
  - Store system time upon receipt of new frame
  - In-stream still-image-capture support
  - Ensure successful stream parameter negotiation
  - Improved build scripts
  - Better logging on Android

Bug fixes:
  - Many build issues
  - Log spam in production builds
  - A situation where libuvc could hang during shutdown
  - A deadlock in get_frame()
  - An issue that could cause the first frame to fail
  - Incorrect settings usage in example code
  - Unnecessary device reopening
  - Excessive number of transfer buffers affecting Mac builds
  - Freeing of transfers that may still be in progress

Changes in 0.0.6 (2017-09-21)
----------------

New features:
  - Added support for pkg-config.
    - Silence pkg-config when searching for libjpeg
    - Resolve pkg-config conflict, update CONF_LIBRARY #11
    - Actually use pkg-config to find libjpeg, and set cflags and ldflags for it. #78
    - Add a pkg-config file to allow build systems to identify the flags needed for compiling and linking against this library  #77
  - Add support for 16-bit greyscale image format (Y16) #77
  - Add support for 8-bit raw colour formats, specifically: BA81, GRBG, GBRG, RGGB and BGGR #77
  - Improve detection of The Imaging Source Cameras that implement the UVC protocols but don't announce themselves as UVC cameras (and ignore similar cameras that appear to be UVC but are not). #77
  - add routine for listing frame configs #76
  - New Travis configuration
  - Add uvc_find_devices function to support libuvc_ros index selection #36
  - VideoStreaming status processing #30
  - Add conversion from YUYV to Y (GRAY8) and UV (GRAY8) #27
  - Add LICENSE.txt.
  - Add Dw_clock_frequency field and populate acording to UVC1.0 or 1.1 spec. #19
  - Changes to enable compilation with Windows MSVC #13
    - Modify ABS_FMT macro to function correctly with MSVC.
    - Implement gettimeofday for MSVC.
    - Use portable memset instead of bzero for MSVC compatibility.
    - Annotate libusb callback methods with LIBUSB_CALL for MSVC.

  - Merge of bulk mode transfer support from Tridge, Mauricio and James (#8)
    - Support for BY8 frames
    - (Very basic) support for UVC1.1 camera data structures
    - Support for frame-based video streams
    - Support for TIS astro CCD cameras
    - Squashed a few possible bugs
    - Added section descriptions and control getter/setter documentation

Bug fixes:
  - update CMakeLists.txt
    - Add missing include (for ${CMAKE_INSTALL_LIBDIR}), fix static-/shared-only build #15
    - Allow both static and dynamic versions of library to be built to work more neatly when building into runtime and development packages #77

  - Update device.c, reduce salience of the alert which end user perceives as more important than it actually is. #79
  - Clean up transfer buffers when an error occurs #77
  - Fixed the issue that camera control functions can not work well with some cameras #41
   UVC specification says that low byte of wIndex (for camera control) is interface number for camera control interface but current implementation of libuvc expects it is always zero and sometimes cause trouble.
   Fixed camera control issue when using uvc_get_ctrl_len/uvc_get_ctrl/uvc_set_ctrl with camera that interface number of camera control interface is not zero.
  - In some (as yet unidentified) circumstances it is possible for an interface to be claimed more than once.  This can cause an error. #77
  - A race allows queued transfers to arrive and be viable for resubmission even when a stream is no longer running. #77
  - Querying stream controls before claiming the interface can fail, so claim the interface first, then do the query. #77
  - Show error code in error message to help debugging #77
  - Add specific error messages for unsupported frame formats to make failures clearer #77
 - Clean up compiler warnings #77
 - Remove libusb.h from the public header #62
 - Fix the first captured image being dropped #51
   The first frame generated by any camera was always lost in the libuvc layer, and not passed on to the listening application.
   The start value of seq (thus hold_seq) could be arbitrary, but it must be non-zero. Both the callback listener and the polling function's last_seq / last_polled_seq start initialised to zero, which causes any image with hold_seq zero to be ignored.
 - Build fix for Chromium #48, #58
   Some platforms use different version of libusb. For instance, Chromium compiles libusb by itself. So hard coding of libuse version (i.e. <libusb-1.0/libusb.h>) causes compile failure.
   Remove libusb.h from the public header, This should fix the include path problems seen when including libuvc.h into other programs
 - pthread_cond_timedwait nanoseconds shouldn't be greater than 1 billion #47
    While using the library and testing the timed poll with 500000 us wait time, I noticed that it often happened that the timedwait returned EINVAL.
According to http://stackoverflow.com/questions/11319063/pthread-cond-timedwait-root-causes-of-einval
this might happen for several reasons, and one of them is when the number of nanoseconds in the timespec is greater than 1 billion (1 billion = 1 second).
    Thus, I added a check that correctly updates the seconds and nanoseconds, bringing the nanosecond values in the range 0-999999999.
    In Issue #16 it is mentioned that code sometimes crashes on uvc_stream_get_frame with some specific timeout values, this might have been the cause.
     Fix for wrong setting of pthread_cond_timedwait nanoseconds
  - Fix control functions for processors and other devices #32
    When using a control function that is pertinent to a particular device (such as a processing device), the id of that device needs to be passed to the camera, or the call will fail
  - Added docs for some more controls and switched to yaml doc file
  - Updated documentation index
  - Enable uvc_mjpeg2rgb function, if JPEG is supported

Changes in 0.0.5 (2014-07-19)
----------------

New features:
 - Added support for all of the camera terminal and processing unit controls, including the controls
   that appeared in UVC 1.1 and 1.5.
 - Added LIBUVC_VERSION_GTE(major, minor, patch) macro.

Bug fixes:
 - Switching to explicit kernel driver detachment since auto_detach isn't available in libusb < 1.0.16.
 - The cmake module now looks for libuvc.dylib instead of libuvc.so on OS X.


Changes in 0.0.4 (2014-06-26)
----------------

New features:
 - Support devices with multiple streaming interfaces and multiple concurrent streams.
   A new uvc_stream* API is added, along with a uvc_stream_handle type to encapsulate the
   state of a single UVC stream. Multiple streams can run alongside each other, provided
   your USB connection has enough bandwidth. Streams can be individually stopped and
   resumed; the old uvc_start/stop_streaming API is still provided as a convenient way
   to interact with the usual one-stream devices.
 - Added support for MJPEG streams.
 - Added functions for checking/setting autofocus mode.
 - Added an interface to set/get arbitrary controls on units and terminals.
 - Made the input, output, processing and extension units public.
 - Implemented uvc_get_device and uvc_get_libusb_handle.
 - Add a library-owned flag to uvc_frame_t so that users may allocate their own frame buffers.

Bug fixes:
 - Send frames as soon as they're received, not when the following frame arrives
 - Fixed call to NULL when no status callback is provided.
 - Fixed crash that occurred during shutdown if the USB device was disconnected during streaming.

Miscellaneous improvements:
 - Hid the transfer method (isochronous vs bulk) from the user. This was never really
   selectable; the camera's streaming interface supports either bulk or isochronous
   transfers, so now libuvc will figure out which one is appropriate. The `isochronous`
   parameter has been converted to a `flags` parameter, which is currently unused but
   could be used to convey up to 7 bits of stream mode information in the future.
 - Improved the method for claiming the camera's interfaces.
 - Renamed UVC_COLOR_FORMAT_* to UVC_FRAME_FORMAT_*. The old #defines are still available.
 - Simplified format definition and lookup.
 - Improved transfer status (error) handling.
