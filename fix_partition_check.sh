#!/bin/bash

# Direct Fix for Partition Check Error
# This script fixes the specific check_sizes.py failure

set -e

echo "=== Fixing Partition Check Error ==="

# Check if we're in the right directory
if [ ! -f "main/main.c" ]; then
    echo "Error: Please run this script from the project root directory"
    exit 1
fi

echo "1. Removing conflicting partition configuration..."

# Remove the custom partition file that's causing conflicts
rm -f partitions.csv

echo "2. Cleaning conflicting configuration..."

# Create a clean sdkconfig.defaults without partition conflicts
cat > sdkconfig.defaults << 'EOF'
# ESP32-S3 Video Streaming Configuration
# Fixed partition table configuration

# Target
CONFIG_IDF_TARGET="esp32s3"

# Partition Table - Single factory app (default)
CONFIG_PARTITION_TABLE_SINGLE_APP=y

# Flash Configuration
CONFIG_ESPTOOLPY_FLASHMODE_QIO=y
CONFIG_ESPTOOLPY_FLASHFREQ_80M=y
CONFIG_ESPTOOLPY_FLASHSIZE_4MB=y

# PSRAM Configuration
CONFIG_SPIRAM=y
CONFIG_SPIRAM_MODE_OCT=y
CONFIG_SPIRAM_SPEED_80M=y

# CPU Configuration
CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ_240=y
CONFIG_FREERTOS_HZ=1000

# Compiler Optimization
CONFIG_COMPILER_OPTIMIZATION_SIZE=y

# Application Configuration
CONFIG_MODE_WIFI_STREAMING=y

# Frame Configuration
CONFIG_SIZE_320x240=y
CONFIG_FORMAT_MJPG=y
CONFIG_FRAME_RATE=15

# Log Configuration
CONFIG_LOG_DEFAULT_LEVEL_INFO=y
CONFIG_BOOTLOADER_LOG_LEVEL_WARN=y

# Memory Configuration
CONFIG_ESP_MAIN_TASK_STACK_SIZE=4096
EOF

echo "3. Cleaning build artifacts..."
rm -rf build/

echo "4. Reconfiguring project..."
# Note: We'll provide manual steps since ESP-IDF environment may not be sourced

echo ""
echo "=== Manual Steps (Run these commands) ==="
echo ""
echo "# Source ESP-IDF environment:"
echo ". \$IDF_PATH/export.sh"
echo ""
echo "# Set target and reconfigure:"
echo "idf.py set-target esp32s3"
echo "idf.py reconfigure"
echo ""
echo "# Build project:"
echo "idf.py build"
echo ""

# Try to run the commands if ESP-IDF is available
if command -v idf.py >/dev/null 2>&1; then
    echo "ESP-IDF detected, running commands automatically..."
    
    echo "Setting target..."
    idf.py set-target esp32s3
    
    echo "Reconfiguring..."
    idf.py reconfigure
    
    echo "Building..."
    if idf.py build; then
        echo ""
        echo "✅ Build successful!"
        echo ""
        echo "=== Next Steps ==="
        echo "Flash to device:"
        echo "  idf.py -p /dev/ttyUSB0 flash monitor"
        echo ""
    else
        echo ""
        echo "❌ Build failed. Try manual steps above."
        echo ""
    fi
else
    echo "ESP-IDF not found in PATH. Please run the manual steps above."
fi

echo "=== Fix Applied ==="
echo ""
echo "What was fixed:"
echo "- Removed conflicting partitions.csv file"
echo "- Set partition table to default single app"
echo "- Cleaned configuration conflicts"
echo "- Reset to working configuration"
echo ""
echo "The project should now build successfully with WiFi streaming enabled."
echo "Display features can be added later via menuconfig."
