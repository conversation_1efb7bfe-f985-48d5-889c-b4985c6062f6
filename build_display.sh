#!/bin/bash

# ESP32-S3-TOUCH-LCD-7 Display Build Script
# This script configures and builds the project for display mode

set -e

echo "=== ESP32-S3-TOUCH-LCD-7 Display Build Script ==="

# Check if ESP-IDF is sourced
if [ -z "$IDF_PATH" ]; then
    echo "Error: ESP-IDF environment not found. Please run:"
    echo ". \$IDF_PATH/export.sh"
    exit 1
fi

# Check ESP-IDF version
IDF_VERSION=$(idf.py --version | grep -o 'v[0-9]\+\.[0-9]\+' | head -1)
echo "ESP-IDF Version: $IDF_VERSION"

if [[ "$IDF_VERSION" < "v5.0" ]]; then
    echo "Warning: ESP-IDF v5.0+ recommended for best compatibility"
fi

# Set target
echo "Setting target to ESP32-S3..."
idf.py set-target esp32s3

# Configure for display mode
echo "Configuring for display mode..."
if [ -f "sdkconfig.defaults.display" ]; then
    cp sdkconfig.defaults.display sdkconfig.defaults
    echo "Applied display-optimized configuration"
else
    echo "Warning: sdkconfig.defaults.display not found, using default settings"
fi

# Build options
BUILD_CLEAN=false
FLASH_DEVICE=""
MONITOR_AFTER_FLASH=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --clean)
            BUILD_CLEAN=true
            shift
            ;;
        --flash)
            FLASH_DEVICE="$2"
            shift 2
            ;;
        --monitor)
            MONITOR_AFTER_FLASH=true
            shift
            ;;
        --help)
            echo "Usage: $0 [options]"
            echo "Options:"
            echo "  --clean          Clean build before compiling"
            echo "  --flash DEVICE   Flash to specified device (e.g., /dev/ttyUSB0)"
            echo "  --monitor        Start monitor after flashing"
            echo "  --help           Show this help message"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Clean build if requested
if [ "$BUILD_CLEAN" = true ]; then
    echo "Cleaning previous build..."
    idf.py fullclean
fi

# Build project
echo "Building project..."
idf.py build

if [ $? -eq 0 ]; then
    echo "✅ Build successful!"
    
    # Show memory usage
    echo ""
    echo "=== Memory Usage ==="
    idf.py size
    
    # Flash if device specified
    if [ -n "$FLASH_DEVICE" ]; then
        echo ""
        echo "Flashing to $FLASH_DEVICE..."
        idf.py -p "$FLASH_DEVICE" flash
        
        if [ $? -eq 0 ]; then
            echo "✅ Flash successful!"
            
            # Start monitor if requested
            if [ "$MONITOR_AFTER_FLASH" = true ]; then
                echo ""
                echo "Starting monitor..."
                idf.py -p "$FLASH_DEVICE" monitor
            fi
        else
            echo "❌ Flash failed!"
            exit 1
        fi
    fi
    
    echo ""
    echo "=== Build Complete ==="
    echo "To flash manually:"
    echo "  idf.py -p /dev/ttyUSB0 flash"
    echo "To monitor:"
    echo "  idf.py -p /dev/ttyUSB0 monitor"
    echo ""
    echo "For WiFi mode, use:"
    echo "  idf.py menuconfig"
    echo "  Navigate to: Application Configuration → Output Mode → WiFi Streaming"
    
else
    echo "❌ Build failed!"
    exit 1
fi
