# Build Instructions for ESP32-S3-TOUCH-LCD-7 Display Enhancement

## Issue Resolution: Component Dependencies

The build error you encountered is due to LVGL component availability in ESP-IDF v5.5. Here's how to resolve it:

## Quick Fix

### Option 1: Use Core ESP-LCD Components (Recommended)

The implementation has been updated to work with core ESP-IDF components without external LVGL dependencies.

```bash
# 1. Source ESP-IDF environment
. $IDF_PATH/export.sh

# 2. Clean any previous build
idf.py fullclean

# 3. Set target
idf.py set-target esp32s3

# 4. Configure for display mode
cp sdkconfig.defaults.display sdkconfig.defaults

# 5. Build
idf.py build
```

### Option 2: Add LVGL Manually (Advanced)

If you want full LVGL integration:

```bash
# 1. Add LVGL as a git submodule
git submodule add https://github.com/lvgl/lvgl.git components/lvgl

# 2. Create LVGL component CMakeLists.txt
cat > components/lvgl/CMakeLists.txt << 'EOF'
idf_component_register(
    SRCS "src/core/lv_disp.c"
         "src/core/lv_group.c"
         "src/core/lv_indev.c"
         "src/core/lv_obj.c"
         "src/core/lv_refr.c"
         "src/draw/lv_draw.c"
         "src/draw/lv_draw_arc.c"
         "src/draw/lv_draw_img.c"
         "src/draw/lv_draw_label.c"
         "src/draw/lv_draw_line.c"
         "src/draw/lv_draw_mask.c"
         "src/draw/lv_draw_rect.c"
         "src/draw/lv_draw_triangle.c"
         "src/draw/sw/lv_draw_sw.c"
         "src/font/lv_font.c"
         "src/font/lv_font_fmt_txt.c"
         "src/hal/lv_hal_disp.c"
         "src/hal/lv_hal_indev.c"
         "src/hal/lv_hal_tick.c"
         "src/misc/lv_anim.c"
         "src/misc/lv_area.c"
         "src/misc/lv_color.c"
         "src/misc/lv_fs.c"
         "src/misc/lv_gc.c"
         "src/misc/lv_ll.c"
         "src/misc/lv_log.c"
         "src/misc/lv_math.c"
         "src/misc/lv_mem.c"
         "src/misc/lv_printf.c"
         "src/misc/lv_style.c"
         "src/misc/lv_style_gen.c"
         "src/misc/lv_task.c"
         "src/misc/lv_templ.c"
         "src/misc/lv_txt.c"
         "src/misc/lv_utils.c"
    INCLUDE_DIRS "src"
    REQUIRES "driver"
)
EOF

# 3. Create lv_conf.h
cat > components/lvgl/lv_conf.h << 'EOF'
#ifndef LV_CONF_H
#define LV_CONF_H

#define LV_COLOR_DEPTH 16
#define LV_COLOR_16_SWAP 0
#define LV_MEM_CUSTOM 1
#define LV_MEM_SIZE (64U * 1024U)
#define LV_USE_PERF_MONITOR 1
#define LV_USE_MEM_MONITOR 1
#define LV_DISP_DEF_REFR_PERIOD 30
#define LV_INDEV_DEF_READ_PERIOD 30
#define LV_TICK_CUSTOM 1
#define LV_DPI_DEF 130

#endif
EOF
```

## Simplified Implementation (Current Status)

The current implementation focuses on core RGB LCD functionality:

### Features Working:
- ✅ RGB LCD panel initialization
- ✅ Frame buffer management
- ✅ JPEG to RGB565 conversion
- ✅ Double buffering
- ✅ Configuration system
- ✅ WiFi streaming preservation

### Features Pending LVGL:
- ⏳ Advanced GUI elements
- ⏳ Touch screen integration
- ⏳ Complex UI layouts

## Build Process

### 1. Environment Setup
```bash
# Ensure ESP-IDF v5.5 is installed and sourced
. $IDF_PATH/export.sh

# Verify version
idf.py --version
```

### 2. Project Configuration
```bash
# Navigate to project directory
cd esp-idf-video-streaming-main

# Set target
idf.py set-target esp32s3

# Apply display configuration
cp sdkconfig.defaults.display sdkconfig.defaults
```

### 3. Build Configuration
```bash
# Optional: Customize settings
idf.py menuconfig

# Key settings to verify:
# - Application Configuration → Output Mode → Local LCD Display
# - Component config → ESP32S3-Specific → Support for external, SPI-connected RAM
# - Component config → ESP32S3-Specific → SPI RAM config → Mode (Octal)
```

### 4. Build and Flash
```bash
# Clean build
idf.py fullclean

# Build
idf.py build

# Flash (replace /dev/ttyUSB0 with your port)
idf.py -p /dev/ttyUSB0 flash monitor
```

## Troubleshooting

### Common Issues and Solutions

1. **Component not found errors:**
   ```bash
   # Remove problematic dependencies
   rm -rf managed_components/
   idf.py fullclean
   idf.py build
   ```

2. **Memory allocation errors:**
   ```bash
   # Ensure PSRAM is enabled
   idf.py menuconfig
   # Component config → ESP32S3-Specific → Support for external, SPI-connected RAM
   ```

3. **Display not working:**
   - Verify pin connections match the configuration
   - Check power supply (5V required)
   - Ensure I/O expander (CH422G) is properly initialized

4. **Build errors:**
   ```bash
   # Reset to clean state
   git checkout -- .
   idf.py fullclean
   rm -rf build/
   idf.py build
   ```

## Testing the Implementation

### 1. WiFi Streaming Mode (Default)
```bash
# Configure for WiFi mode
idf.py menuconfig
# Application Configuration → Output Mode → WiFi Streaming

# Build and flash
idf.py build flash monitor

# Connect to WiFi and access: http://[ESP32_IP]:8080
```

### 2. Local Display Mode
```bash
# Configure for display mode
idf.py menuconfig
# Application Configuration → Output Mode → Local LCD Display

# Build and flash
idf.py build flash monitor

# Video should appear on LCD panel
```

## Hardware Verification

### Pin Connections for ESP32-S3-TOUCH-LCD-7:
```
RGB Data (16-bit):
- B3-B7: GPIO14, GPIO38, GPIO18, GPIO17, GPIO10
- G2-G7: GPIO39, GPIO0, GPIO45, GPIO48, GPIO47, GPIO21  
- R3-R7: GPIO1, GPIO2, GPIO42, GPIO41, GPIO40

Control:
- PCLK:  GPIO7
- HSYNC: GPIO46
- VSYNC: GPIO3
- DE:    GPIO5

I2C (for I/O expander and touch):
- SDA:   GPIO8
- SCL:   GPIO9
- IRQ:   GPIO4
```

## Performance Optimization

### Recommended Settings:
```
CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ_240=y
CONFIG_SPIRAM=y
CONFIG_SPIRAM_MODE_OCT=y
CONFIG_SPIRAM_SPEED_120M=y
CONFIG_COMPILER_OPTIMIZATION_PERF=y
CONFIG_FREERTOS_HZ=1000
```

## Next Steps

1. **Test Basic Functionality:**
   - Verify display initialization
   - Check frame display
   - Test mode switching

2. **Add LVGL (Optional):**
   - Follow Option 2 above for full GUI support
   - Implement touch screen integration

3. **Optimize Performance:**
   - Fine-tune frame rates
   - Adjust buffer sizes
   - Monitor memory usage

The implementation provides a solid foundation for ESP32-S3 video display functionality. The modular design allows for easy enhancement with LVGL when needed.
