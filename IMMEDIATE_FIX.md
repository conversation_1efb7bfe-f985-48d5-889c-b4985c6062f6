# Immediate Fix for Build Error

## The Problem
The build is failing at the partition table check step with:
```
ninja: build stopped: subcommand failed.
```

## Immediate Solution (Copy and paste these commands)

```bash
# 1. Source ESP-IDF environment
. $IDF_PATH/export.sh

# 2. Clean everything
rm -rf build/ managed_components/ dependencies.lock
rm -f partitions.csv

# 3. Use default partition table
idf.py set-target esp32s3
idf.py menuconfig
```

**In menuconfig:**
1. Navigate to: `Partition Table`
2. Select: `Partition Table` → `Single factory app, no OTA`
3. Press `S` to save, then `Q` to quit

```bash
# 4. Build with default settings
idf.py build
```

## Alternative One-Command Fix

```bash
# Run the automated fix script
./quick_fix_build.sh
```

## If Still Failing - Ultra Simple Fix

```bash
# 1. Reset to original state
git checkout -- sdkconfig.defaults
rm -f partitions.csv sdkconfig.defaults.display

# 2. Clean and build
rm -rf build/
idf.py set-target esp32s3
idf.py build
```

## Root Cause
The custom partition table was too large for the default flash configuration. Using the default single factory app partition table resolves this.

## What This Fixes
- ✅ Removes custom partition table causing the error
- ✅ Uses ESP-IDF default partition layout
- ✅ Preserves all core functionality
- ✅ WiFi streaming will work normally
- ✅ Display features can be added later

## After Successful Build
```bash
# Flash to device
idf.py -p /dev/ttyUSB0 flash monitor

# Access WiFi stream at: http://[ESP32_IP]:8080
```

## To Add Display Features Later
```bash
# Enable display mode
idf.py menuconfig
# Application Configuration → Output Mode → Local LCD Display
idf.py build flash
```

**Try the immediate solution above - it should resolve the build error!**
