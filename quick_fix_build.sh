#!/bin/bash

# Quick Build Fix Script
# Resolves the partition table build error by using default settings

set -e

echo "=== Quick Build Fix for ESP32-S3 Video Streaming ==="

# Check if ESP-IDF is sourced
if [ -z "$IDF_PATH" ]; then
    echo "Error: ESP-IDF environment not found. Please run:"
    echo ". \$IDF_PATH/export.sh"
    exit 1
fi

echo "1. Cleaning build artifacts..."
rm -rf build/ managed_components/ dependencies.lock 2>/dev/null || true

echo "2. Removing custom partition table..."
rm -f partitions.csv

echo "3. Creating minimal working configuration..."
cat > sdkconfig.defaults << 'EOF'
# Minimal ESP32-S3 Configuration for Video Streaming

# Target Configuration
CONFIG_IDF_TARGET="esp32s3"

# Partition Table - Use default single factory app
CONFIG_PARTITION_TABLE_SINGLE_APP=y

# Flash Configuration
CONFIG_ESPTOOLPY_FLASHMODE_QIO=y
CONFIG_ESPTOOLPY_FLASHFREQ_80M=y
CONFIG_ESPTOOLPY_FLASHSIZE_4MB=y

# PSRAM Configuration
CONFIG_SPIRAM=y
CONFIG_SPIRAM_MODE_OCT=y
CONFIG_SPIRAM_SPEED_80M=y

# CPU Configuration
CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ_240=y
CONFIG_FREERTOS_HZ=1000

# Compiler Optimization
CONFIG_COMPILER_OPTIMIZATION_SIZE=y

# Application Configuration - Default to WiFi streaming
CONFIG_MODE_WIFI_STREAMING=y

# Frame Configuration
CONFIG_SIZE_320x240=y
CONFIG_FORMAT_MJPG=y
CONFIG_FRAME_RATE=15

# Log Configuration
CONFIG_LOG_DEFAULT_LEVEL_INFO=y
CONFIG_BOOTLOADER_LOG_LEVEL_WARN=y

# USB Host Configuration
CONFIG_USB_HOST_CONTROL_TRANSFER_MAX_SIZE=512

# Memory Configuration
CONFIG_ESP_MAIN_TASK_STACK_SIZE=4096
CONFIG_ESP_SYSTEM_EVENT_TASK_STACK_SIZE=2048

# WiFi Configuration
CONFIG_ESP_WIFI_STATIC_RX_BUFFER_NUM=10
CONFIG_ESP_WIFI_DYNAMIC_RX_BUFFER_NUM=32
CONFIG_ESP_WIFI_STATIC_TX_BUFFER_NUM=16
CONFIG_ESP_WIFI_DYNAMIC_TX_BUFFER_NUM=32

# HTTP Server Configuration
CONFIG_HTTPD_MAX_REQ_HDR_LEN=512
CONFIG_HTTPD_MAX_URI_LEN=256
EOF

echo "4. Setting target..."
idf.py set-target esp32s3

echo "5. Building project..."
echo "This may take a few minutes..."

if idf.py build; then
    echo ""
    echo "✅ Build successful!"
    echo ""
    echo "=== Build Summary ==="
    idf.py size
    echo ""
    echo "=== Next Steps ==="
    echo "1. Flash to device:"
    echo "   idf.py -p /dev/ttyUSB0 flash monitor"
    echo ""
    echo "2. Access WiFi stream:"
    echo "   Connect to WiFi network and visit: http://[ESP32_IP]:8080"
    echo ""
    echo "3. To enable display mode later:"
    echo "   idf.py menuconfig"
    echo "   Application Configuration → Output Mode → Local LCD Display"
    echo "   idf.py build flash"
    echo ""
    echo "=== Configuration Notes ==="
    echo "- Using default partition table (single factory app)"
    echo "- WiFi streaming mode enabled by default"
    echo "- Display mode can be enabled via menuconfig"
    echo "- Frame rate set to 15 FPS for stability"
    echo ""
else
    echo ""
    echo "❌ Build failed!"
    echo ""
    echo "=== Troubleshooting ==="
    echo "1. Check ESP-IDF version (should be 5.0+):"
    echo "   idf.py --version"
    echo ""
    echo "2. Try with even smaller configuration:"
    echo "   idf.py menuconfig"
    echo "   Component config → Log output → Default log verbosity → Error"
    echo "   Component config → FreeRTOS → Kernel → configTICK_RATE_HZ → 100"
    echo ""
    echo "3. Check flash size on your board:"
    echo "   idf.py menuconfig"
    echo "   Serial flasher config → Flash size"
    echo ""
    echo "4. If all else fails, try original configuration:"
    echo "   git checkout -- sdkconfig.defaults"
    echo "   idf.py build"
    echo ""
    exit 1
fi

echo "=== Quick Fix Complete ==="
