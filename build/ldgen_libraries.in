$<TARGET_FILE:idf::xtensa>
$<TARGET_FILE:__idf_cxx>
$<TARGET_FILE:__idf_newlib>
$<TARGET_FILE:__idf_freertos>
$<TARGET_FILE:__idf_esp_hw_support>
$<TARGET_FILE:__idf_heap>
$<TARGET_FILE:__idf_log>
$<TARGET_FILE:__idf_soc>
$<TARGET_FILE:__idf_hal>
$<TARGET_FILE:__idf_esp_rom>
$<TARGET_FILE:__idf_esp_common>
$<TARGET_FILE:__idf_esp_system>
$<TARGET_FILE:__idf_xtensa>
$<TARGET_FILE:__idf_spi_flash>
$<TARGET_FILE:__idf_bootloader_support>
$<TARGET_FILE:__idf_mbedtls>
$<TARGET_FILE:__idf_esp_pm>
$<TARGET_FILE:__idf_esp_driver_gpio>
$<TARGET_FILE:__idf_esp_timer>
$<TARGET_FILE:__idf_lwip>
$<TARGET_FILE:__idf_vfs>
$<TARGET_FILE:__idf_esp_driver_uart>
$<TARGET_FILE:__idf_esp_ringbuf>
$<TARGET_FILE:__idf_esp_mm>
$<TARGET_FILE:idf::vfs>
$<TARGET_FILE:__idf_esp_driver_usb_serial_jtag>
$<TARGET_FILE:__idf_esp_vfs_console>
$<TARGET_FILE:mbedtls>
$<TARGET_FILE:mbedx509>
$<TARGET_FILE:mbedcrypto>
$<TARGET_FILE:everest>
$<TARGET_FILE:p256m>
$<TARGET_FILE:idf::esp_security>
$<TARGET_FILE:__idf_efuse>
$<TARGET_FILE:__idf_esp_partition>
$<TARGET_FILE:__idf_app_update>
$<TARGET_FILE:__idf_esp_app_format>
$<TARGET_FILE:__idf_esp_bootloader_format>
$<TARGET_FILE:idf::esp_mm>
$<TARGET_FILE:__idf_esp_psram>
$<TARGET_FILE:__idf_esp_gdbstub>
$<TARGET_FILE:__idf_driver>
$<TARGET_FILE:__idf_esp_driver_pcnt>
$<TARGET_FILE:__idf_esp_driver_gptimer>
$<TARGET_FILE:__idf_esp_driver_spi>
$<TARGET_FILE:__idf_esp_driver_mcpwm>
$<TARGET_FILE:__idf_esp_driver_i2s>
$<TARGET_FILE:__idf_esp_driver_sdmmc>
$<TARGET_FILE:__idf_sdmmc>
$<TARGET_FILE:__idf_esp_driver_sdspi>
$<TARGET_FILE:__idf_esp_driver_rmt>
$<TARGET_FILE:__idf_esp_driver_tsens>
$<TARGET_FILE:__idf_esp_driver_sdm>
$<TARGET_FILE:__idf_esp_driver_i2c>
$<TARGET_FILE:__idf_esp_driver_ledc>
$<TARGET_FILE:__idf_esp_driver_twai>
$<TARGET_FILE:__idf_esp_adc>
$<TARGET_FILE:__idf_esp_http_client>
$<TARGET_FILE:__idf_esp_event>
$<TARGET_FILE:__idf_tcp_transport>
$<TARGET_FILE:__idf_esp-tls>
$<TARGET_FILE:__idf_http_parser>
$<TARGET_FILE:__idf_esp_http_server>
$<TARGET_FILE:__idf_nvs_flash>
$<TARGET_FILE:idf::mbedtls>
$<TARGET_FILE:__idf_esp_wifi>
$<TARGET_FILE:__idf_esp_phy>
$<TARGET_FILE:__idf_esp_netif>
$<TARGET_FILE:__idf_wpa_supplicant>
$<TARGET_FILE:__idf_esp_coex>
$<TARGET_FILE:__idf_esp_https_ota>
$<TARGET_FILE:__idf_esp_security>
$<TARGET_FILE:__idf_pthread>
$<TARGET_FILE:idf::esp_driver_gpio>
$<TARGET_FILE:idf::esp_pm>
$<TARGET_FILE:idf::esp_app_format>
$<TARGET_FILE:idf::esp_bootloader_format>
$<TARGET_FILE:idf::app_update>
$<TARGET_FILE:idf::esp_partition>
$<TARGET_FILE:idf::efuse>
$<TARGET_FILE:idf::bootloader_support>
$<TARGET_FILE:idf::spi_flash>
$<TARGET_FILE:idf::esp_system>
$<TARGET_FILE:idf::esp_common>
$<TARGET_FILE:idf::esp_rom>
$<TARGET_FILE:idf::hal>
$<TARGET_FILE:idf::log>
$<TARGET_FILE:idf::heap>
$<TARGET_FILE:idf::soc>
$<TARGET_FILE:idf::esp_hw_support>
$<TARGET_FILE:idf::freertos>
$<TARGET_FILE:idf::newlib>
$<TARGET_FILE:idf::pthread>
$<TARGET_FILE:idf::cxx>
$<TARGET_FILE:idf::esp_timer>
$<TARGET_FILE:idf::esp_driver_gptimer>
$<TARGET_FILE:idf::esp_ringbuf>
$<TARGET_FILE:idf::esp_driver_uart>
$<TARGET_FILE:idf::app_trace>
$<TARGET_FILE:idf::esp_event>
$<TARGET_FILE:idf::nvs_flash>
$<TARGET_FILE:idf::esp_phy>
$<TARGET_FILE:idf::esp_driver_usb_serial_jtag>
$<TARGET_FILE:idf::esp_vfs_console>
$<TARGET_FILE:idf::lwip>
$<TARGET_FILE:idf::esp_netif>
$<TARGET_FILE:idf::wpa_supplicant>
$<TARGET_FILE:idf::esp_coex>
$<TARGET_FILE:idf::esp_wifi>
$<TARGET_FILE:idf::esp_driver_spi>
$<TARGET_FILE:idf::esp_gdbstub>
$<TARGET_FILE:idf::unity>
$<TARGET_FILE:idf::cmock>
$<TARGET_FILE:__idf_unity>
$<TARGET_FILE:idf::console>
$<TARGET_FILE:idf::esp_driver_pcnt>
$<TARGET_FILE:idf::esp_driver_mcpwm>
$<TARGET_FILE:idf::esp_driver_i2s>
$<TARGET_FILE:idf::sdmmc>
$<TARGET_FILE:idf::esp_driver_sdmmc>
$<TARGET_FILE:idf::esp_driver_sdspi>
$<TARGET_FILE:idf::esp_driver_rmt>
$<TARGET_FILE:idf::esp_driver_tsens>
$<TARGET_FILE:idf::esp_driver_sdm>
$<TARGET_FILE:idf::esp_driver_i2c>
$<TARGET_FILE:idf::esp_driver_ledc>
$<TARGET_FILE:idf::esp_driver_twai>
$<TARGET_FILE:idf::driver>
$<TARGET_FILE:idf::http_parser>
$<TARGET_FILE:idf::esp-tls>
$<TARGET_FILE:idf::esp_adc>
$<TARGET_FILE:idf::esp_driver_cam>
$<TARGET_FILE:idf::esp_psram>
$<TARGET_FILE:idf::esp_driver_touch_sens>
$<TARGET_FILE:idf::esp_eth>
$<TARGET_FILE:idf::esp_hid>
$<TARGET_FILE:idf::tcp_transport>
$<TARGET_FILE:idf::esp_http_client>
$<TARGET_FILE:idf::esp_http_server>
$<TARGET_FILE:idf::esp_https_ota>
$<TARGET_FILE:idf::esp_https_server>
$<TARGET_FILE:idf::esp_lcd>
$<TARGET_FILE:idf::protobuf-c>
$<TARGET_FILE:idf::protocomm>
$<TARGET_FILE:__idf_protobuf-c>
$<TARGET_FILE:__idf_console>
$<TARGET_FILE:idf::esp_local_ctrl>
$<TARGET_FILE:__idf_protocomm>
$<TARGET_FILE:__idf_esp_https_server>
$<TARGET_FILE:__idf_espressif__mdns>
$<TARGET_FILE:__idf_esp_eth>
$<TARGET_FILE:idf::espcoredump>
$<TARGET_FILE:idf::wear_levelling>
$<TARGET_FILE:idf::fatfs>
$<TARGET_FILE:__idf_wear_levelling>
$<TARGET_FILE:idf::json>
$<TARGET_FILE:idf::mqtt>
$<TARGET_FILE:idf::nvs_sec_provider>
$<TARGET_FILE:idf::perfmon>
$<TARGET_FILE:idf::rt>
$<TARGET_FILE:idf::spiffs>
$<TARGET_FILE:idf::touch_element>
$<TARGET_FILE:idf::usb>
$<TARGET_FILE:idf::wifi_provisioning>
$<TARGET_FILE:__idf_json>
$<TARGET_FILE:idf::espressif__mdns>
$<TARGET_FILE:idf::espressif__usb_host_uvc>
$<TARGET_FILE:__idf_usb>
$<TARGET_FILE:idf::main>
$<TARGET_FILE:__idf_espressif__usb_host_uvc>
$<TARGET_FILE:idf::conversions>