#!/bin/bash

# Fix Partition Table Error Script
# Resolves the "ninja: build stopped: subcommand failed" error

set -e

echo "=== Fixing Partition Table Build Error ==="

# Check if we're in the right directory
if [ ! -f "main/main.c" ]; then
    echo "Error: Please run this script from the project root directory"
    exit 1
fi

# Check if ESP-IDF is sourced
if [ -z "$IDF_PATH" ]; then
    echo "Error: ESP-IDF environment not found. Please run:"
    echo ". \$IDF_PATH/export.sh"
    exit 1
fi

echo "1. Cleaning build artifacts..."
rm -rf build/ managed_components/ dependencies.lock 2>/dev/null || true

echo "2. Creating custom partition table..."
cat > partitions.csv << 'EOF'
# ESP32-S3 Partition Table for Video Streaming with Display
# Name,   Type, SubType, Offset,  Size,    Flags
nvs,      data, nvs,     0x9000,  0x6000,
phy_init, data, phy,     0xf000,  0x1000,
factory,  app,  factory, 0x10000, 0xF00000,
storage,  data, spiffs,  ,        0xF0000,
EOF

echo "3. Updating configuration..."
# Ensure we have the display configuration
if [ ! -f "sdkconfig.defaults.display" ]; then
    echo "Creating sdkconfig.defaults.display..."
    cp sdkconfig.defaults sdkconfig.defaults.display 2>/dev/null || touch sdkconfig.defaults.display
fi

# Add partition table configuration if not present
if ! grep -q "CONFIG_PARTITION_TABLE_CUSTOM" sdkconfig.defaults.display; then
    echo "Adding partition table configuration..."
    cat >> sdkconfig.defaults.display << 'EOF'

# Partition Table Configuration
CONFIG_PARTITION_TABLE_CUSTOM=y
CONFIG_PARTITION_TABLE_CUSTOM_FILENAME="partitions.csv"

# Bootloader Configuration
CONFIG_BOOTLOADER_LOG_LEVEL_WARN=y

# Application Size Configuration
CONFIG_APP_COMPILE_TIME_DATE=n
CONFIG_APP_EXCLUDE_PROJECT_VER_VAR=y
CONFIG_APP_EXCLUDE_PROJECT_NAME_VAR=y
EOF
fi

echo "4. Applying configuration..."
cp sdkconfig.defaults.display sdkconfig.defaults

echo "5. Setting target..."
idf.py set-target esp32s3

echo "6. Reconfiguring project..."
idf.py reconfigure

echo "7. Testing build..."
echo "Building project (this may take a few minutes)..."

if idf.py build; then
    echo ""
    echo "✅ Build successful!"
    echo ""
    echo "=== Build Summary ==="
    idf.py size
    echo ""
    echo "Next steps:"
    echo "1. Flash to device:"
    echo "   idf.py -p /dev/ttyUSB0 flash monitor"
    echo ""
    echo "2. For WiFi mode, run:"
    echo "   idf.py menuconfig"
    echo "   Navigate to: Application Configuration → Output Mode → WiFi Streaming"
    echo ""
else
    echo ""
    echo "❌ Build failed!"
    echo ""
    echo "Troubleshooting steps:"
    echo "1. Check ESP-IDF version:"
    echo "   idf.py --version"
    echo ""
    echo "2. Try full clean:"
    echo "   idf.py fullclean"
    echo "   idf.py build"
    echo ""
    echo "3. Check available flash size:"
    echo "   idf.py menuconfig"
    echo "   Serial flasher config → Flash size"
    echo ""
    echo "4. If issues persist, try default partition table:"
    echo "   rm partitions.csv"
    echo "   idf.py menuconfig"
    echo "   Partition Table → Partition Table → Single factory app, no OTA"
    echo ""
    exit 1
fi

echo "=== Fix Complete ==="
