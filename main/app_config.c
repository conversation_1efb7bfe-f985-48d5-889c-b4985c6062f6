/*
 * SPDX-FileCopyrightText: 2024 Espressif Systems (Shanghai) CO LTD
 *
 * SPDX-License-Identifier: Apache-2.0
 */

#include "app_config.h"
#include "esp_log.h"
#include <string.h>

static const char *TAG = "APP_CONFIG";

static app_config_t s_app_config = {0};
static bool s_config_initialized = false;

esp_err_t app_config_init(void)
{
    if (s_config_initialized) {
        ESP_LOGW(TAG, "Configuration already initialized");
        return ESP_OK;
    }
    
    ESP_LOGI(TAG, "Initializing application configuration");
    
    // Set output mode based on compile-time configuration
#if APP_MODE_WIFI_STREAMING && APP_MODE_LOCAL_DISPLAY
    s_app_config.output_mode = APP_OUTPUT_MODE_DUAL;
    s_app_config.wifi_enabled = true;
    s_app_config.display_enabled = true;
    strcpy(s_app_config.mode_description, "Dual Mode (WiFi + Display)");
#elif APP_MODE_LOCAL_DISPLAY
    s_app_config.output_mode = APP_OUTPUT_MODE_LOCAL_DISPLAY;
    s_app_config.wifi_enabled = false;
    s_app_config.display_enabled = true;
    strcpy(s_app_config.mode_description, "Local Display Mode");
#else
    s_app_config.output_mode = APP_OUTPUT_MODE_WIFI_STREAMING;
    s_app_config.wifi_enabled = true;
    s_app_config.display_enabled = false;
    strcpy(s_app_config.mode_description, "WiFi Streaming Mode");
#endif
    
    // Set touch configuration
    s_app_config.touch_enabled = APP_ENABLE_TOUCH && s_app_config.display_enabled;
    
    // Set frame configuration
    s_app_config.frame_width = APP_FRAME_WIDTH;
    s_app_config.frame_height = APP_FRAME_HEIGHT;
    s_app_config.frame_rate = APP_FRAME_RATE;
    
    // Set display buffer configuration
    s_app_config.display_buffer_count = APP_DISPLAY_BUFFER_SIZE;
    
    s_config_initialized = true;
    
    ESP_LOGI(TAG, "onfiguration initialized successfully");
    app_config_print();
    
    return ESP_OK;
}

app_config_t* app_config_get(void)
{
    if (!s_config_initialized) {
        ESP_LOGW(TAG, "Configuration not initialized, initializing with defaults");
        app_config_init();
    }
    return &s_app_config;
}

void app_config_print(void)
{
    if (!s_config_initialized) {
        ESP_LOGW(TAG, "Configuration not initialized");
        return;
    }
    
    ESP_LOGI(TAG, "=== Application Configuration ===");
    ESP_LOGI(TAG, "Mode: %s", s_app_config.mode_description);
    ESP_LOGI(TAG, "WiFi Streaming: %s", s_app_config.wifi_enabled ? "Enabled" : "Disabled");
    ESP_LOGI(TAG, "Local DCisplay: %s", s_app_config.display_enabled ? "Enabled" : "Disabled");
    ESP_LOGI(TAG, "Touch Screen: %s", s_app_config.touch_enabled ? "Enabled" : "Disabled");
    ESP_LOGI(TAG, "Frame Size: %dx%d", s_app_config.frame_width, s_app_config.frame_height);
    ESP_LOGI(TAG, "Frame Rate: %d fps", s_app_config.frame_rate);
    ESP_LOGI(TAG, "Frame Format: %s", APP_FRAME_FORMAT_STR);
    if (s_app_config.display_enabled) {
        ESP_LOGI(TAG, "Display Buffers: %d", s_app_config.display_buffer_count);
    }
    ESP_LOGI(TAG, "================================");
}

bool app_config_is_wifi_enabled(void)
{
    if (!s_config_initialized) {
        app_config_init();
    }
    return s_app_config.wifi_enabled;
}

bool app_config_is_display_enabled(void)
{
    if (!s_config_initialized) {
        app_config_init();
    }
    return s_app_config.display_enabled;
}

bool app_config_is_touch_enabled(void)
{
    if (!s_config_initialized) {
        app_config_init();
    }
    return s_app_config.touch_enabled;
}
