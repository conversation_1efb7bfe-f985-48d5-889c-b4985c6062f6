/*
 * SPDX-FileCopyrightText: 2024 Espressif Systems (Shanghai) CO LTD
 *
 * SPDX-License-Identifier: Apache-2.0
 */

#pragma once

#include <stdint.h>
#include <stdbool.h>
#include "esp_err.h"
#include "esp_lcd_panel_rgb.h"
// LVGL includes will be added when esp_lvgl_port is available
// #include "esp_lvgl_port.h"
// #include "lvgl.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief ESP32-S3-TOUCH-LCD-7 display configuration
 */
#define DISPLAY_WIDTH           800
#define DISPLAY_HEIGHT          480
#define DISPLAY_COLOR_DEPTH     16  // RGB565

// RGB LCD pins for ESP32-S3-TOUCH-LCD-7
#define LCD_PIXEL_CLOCK_HZ      (21 * 1000 * 1000)
#define LCD_BK_LIGHT_ON_LEVEL   1
#define LCD_BK_LIGHT_OFF_LEVEL  !LCD_BK_LIGHT_ON_LEVEL

// RGB Data pins (16-bit RGB565 format)
// Blue pins (B4-B0)
#define PIN_NUM_DATA0           14  // B3
#define PIN_NUM_DATA1           38  // B4
#define PIN_NUM_DATA2           18  // B5
#define PIN_NUM_DATA3           17  // B6
#define PIN_NUM_DATA4           10  // B7
// Green pins (G5-G0)
#define PIN_NUM_DATA5           39  // G2
#define PIN_NUM_DATA6           0   // G3
#define PIN_NUM_DATA7           45  // G4
#define PIN_NUM_DATA8           48  // G5
#define PIN_NUM_DATA9           47  // G6
#define PIN_NUM_DATA10          21  // G7
// Red pins (R4-R0)
#define PIN_NUM_DATA11          1   // R3
#define PIN_NUM_DATA12          2   // R4
#define PIN_NUM_DATA13          42  // R5
#define PIN_NUM_DATA14          41  // R6
#define PIN_NUM_DATA15          40  // R7

// RGB Control pins
#define PIN_NUM_PCLK            7   // Pixel clock
#define PIN_NUM_CS              -1  // Not used for RGB
#define PIN_NUM_DC              -1  // Not used for RGB
#define PIN_NUM_RST             -1  // Controlled via I/O expander
#define PIN_NUM_BK_LIGHT        -1  // Controlled via I/O expander

// Sync pins
#define PIN_NUM_HSYNC           46  // Horizontal sync
#define PIN_NUM_VSYNC           3   // Vertical sync
#define PIN_NUM_DE              5   // Data enable

// Touch pins (I2C)
#define PIN_NUM_TOUCH_SDA       8
#define PIN_NUM_TOUCH_SCL       9
#define PIN_NUM_TOUCH_IRQ       4
#define PIN_NUM_TOUCH_RST       -1  // Controlled via I/O expander

// I/O Expander (CH422G) I2C address
#define CH422G_I2C_ADDR         0x24

// I/O Expander pin definitions
#define EXIO_LCD_RST            2   // LCD reset pin on CH422G
#define EXIO_LCD_BL             3   // LCD backlight pin on CH422G
#define EXIO_TOUCH_RST          1   // Touch reset pin on CH422G

/**
 * @brief Display driver handle
 */
typedef struct {
    esp_lcd_panel_handle_t panel_handle;
    // LVGL handles will be added when esp_lvgl_port is available
    // lv_display_t *lvgl_display;
    // lv_indev_t *touch_indev;
    void *lvgl_display;      // Placeholder for future LVGL integration
    void *touch_indev;       // Placeholder for future touch integration
    bool initialized;
    uint8_t *frame_buffer1;
    uint8_t *frame_buffer2;
    size_t buffer_size;
} display_driver_t;

/**
 * @brief Initialize the display driver
 * 
 * @return esp_err_t ESP_OK on success
 */
esp_err_t display_driver_init(void);

/**
 * @brief Deinitialize the display driver
 * 
 * @return esp_err_t ESP_OK on success
 */
esp_err_t display_driver_deinit(void);

/**
 * @brief Display a video frame on the LCD
 * 
 * @param frame_data Pointer to frame data (JPEG or RGB565)
 * @param frame_size Size of frame data in bytes
 * @param is_jpeg True if frame data is JPEG, false if RGB565
 * @return esp_err_t ESP_OK on success
 */
esp_err_t display_show_frame(const uint8_t *frame_data, size_t frame_size, bool is_jpeg);

/**
 * @brief Convert JPEG frame to RGB565 for display
 * 
 * @param jpeg_data Pointer to JPEG data
 * @param jpeg_size Size of JPEG data
 * @param rgb_buffer Output buffer for RGB565 data
 * @param rgb_buffer_size Size of RGB buffer
 * @return esp_err_t ESP_OK on success
 */
esp_err_t display_convert_jpeg_to_rgb565(const uint8_t *jpeg_data, size_t jpeg_size, 
                                        uint8_t *rgb_buffer, size_t rgb_buffer_size);

/**
 * @brief Get display driver handle
 * 
 * @return display_driver_t* Pointer to display driver handle
 */
display_driver_t* display_get_driver(void);

/**
 * @brief Check if display is initialized
 * 
 * @return true if initialized, false otherwise
 */
bool display_is_initialized(void);

#ifdef __cplusplus
}
#endif
