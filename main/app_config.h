/*
 * SPDX-FileCopyrightText: 2024 Espressif Systems (Shanghai) CO LTD
 *
 * SPDX-License-Identifier: Apache-2.0
 */

#pragma once

#include "sdkconfig.h"
#include "esp_log.h"
#include "esp_err.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief Application configuration and mode management
 */

// Output mode configuration
#ifdef CONFIG_MODE_WIFI_STREAMING
#define APP_MODE_WIFI_STREAMING     1
#define APP_MODE_LOCAL_DISPLAY      0
#elif CONFIG_MODE_LOCAL_DISPLAY
#define APP_MODE_WIFI_STREAMING     0
#define APP_MODE_LOCAL_DISPLAY      1
#else
// Default to WiFi streaming if no mode is selected
#define APP_MODE_WIFI_STREAMING     0
#define APP_MODE_LOCAL_DISPLAY      1 //NBP
#endif

// Touch screen configuration
#ifdef CONFIG_ENABLE_TOUCH
#define APP_ENABLE_TOUCH            1
#else
#define APP_ENABLE_TOUCH            0
#endif

// Display buffer configuration
#ifdef CONFIG_DISPLAY_BUFFER_SIZE
#define APP_DISPLAY_BUFFER_SIZE     CONFIG_DISPLAY_BUFFER_SIZE
#else
#define APP_DISPLAY_BUFFER_SIZE     2
#endif

// Frame size configuration
#ifdef CONFIG_SIZE_640x480
#define APP_FRAME_WIDTH             640
#define APP_FRAME_HEIGHT            480
#elif CONFIG_SIZE_352x288
#define APP_FRAME_WIDTH             352
#define APP_FRAME_HEIGHT            288
#elif CONFIG_SIZE_320x240
#define APP_FRAME_WIDTH             320
#define APP_FRAME_HEIGHT            240
#elif CONFIG_SIZE_160x120
#define APP_FRAME_WIDTH             160
#define APP_FRAME_HEIGHT            120
#else
#define APP_FRAME_WIDTH             320
#define APP_FRAME_HEIGHT            240
#endif

// Frame format configuration
#ifdef CONFIG_FORMAT_MJPG
#define APP_FRAME_FORMAT            UVC_COLOR_FORMAT_MJPEG
#define APP_FRAME_FORMAT_STR        "MJPEG"
#elif CONFIG_FORMAT_YUY2
#define APP_FRAME_FORMAT            UVC_COLOR_FORMAT_YUYV
#define APP_FRAME_FORMAT_STR        "YUYV"
#else
#define APP_FRAME_FORMAT            UVC_COLOR_FORMAT_MJPEG
#define APP_FRAME_FORMAT_STR        "MJPEG"
#endif

// Frame rate configuration
#ifdef CONFIG_FRAME_RATE
#define APP_FRAME_RATE              CONFIG_FRAME_RATE
#else
#define APP_FRAME_RATE              30
#endif

/**
 * @brief Application mode enumeration
 */
typedef enum {
    APP_OUTPUT_MODE_WIFI_STREAMING = 0,
    APP_OUTPUT_MODE_LOCAL_DISPLAY = 1,
    APP_OUTPUT_MODE_DUAL = 2,  // Future enhancement
} app_output_mode_t;

/**
 * @brief Application configuration structure
 */
typedef struct {
    app_output_mode_t output_mode;
    bool wifi_enabled;
    bool display_enabled;
    bool touch_enabled;
    uint16_t frame_width;
    uint16_t frame_height;
    uint8_t frame_rate;
    uint8_t display_buffer_count;
    char mode_description[64];
} app_config_t;

/**
 * @brief Get current application configuration
 * 
 * @return app_config_t* Pointer to application configuration
 */
app_config_t* app_config_get(void);

/**
 * @brief Initialize application configuration
 * 
 * @return esp_err_t ESP_OK on success
 */
esp_err_t app_config_init(void);

/**
 * @brief Print current configuration
 */
void app_config_print(void);

/**
 * @brief Check if WiFi streaming is enabled
 * 
 * @return true if WiFi streaming is enabled
 */
bool app_config_is_wifi_enabled(void);

/**
 * @brief Check if local display is enabled
 * 
 * @return true if local display is enabled
 */
bool app_config_is_display_enabled(void);

/**
 * @brief Check if touch screen is enabled
 * 
 * @return true if touch screen is enabled
 */
bool app_config_is_touch_enabled(void);

#ifdef __cplusplus
}
#endif
